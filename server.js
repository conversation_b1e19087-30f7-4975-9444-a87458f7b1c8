// Load environment variables from .env and .env.local (if it exists)
require('dotenv').config();
try {
  require('dotenv').config({ path: '.env.local', override: true });
  console.log("Loaded environment variables from .env.local");
} catch (error) {
  console.log("No .env.local file found, using .env variables");
}

// Log the MongoDB URI (masked for security)//
console.log("MongoDB URI (masked):", 
  process.env.MONGODB_URI ? 
  process.env.MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, "//****:****@") : 
  "Not defined");

const express = require("express");
const mongoose = require("mongoose");
const bodyParser = require("body-parser");
const session = require("express-session");
const path = require("path");
const bcrypt = require("bcryptjs");
const cors = require("cors");
const jwt = require("jsonwebtoken"); // For token-based authentication
const multer = require("multer");
const fs = require("fs");
const app = express();
const router = express.Router();
const port = process.env.PORT || 3000;
const PDFDocument = require('pdfkit');
const { sendWelcomeEmail } = require('./config/emailConfig');
const MongoStore = require('connect-mongo');
const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const csv = require('csv-parser');
const models = require('./models'); // <-- ADD THIS LINE
const { body, validationResult } = require('express-validator');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// Serve static files from the "public" directory
app.use(express.static(path.join(__dirname, 'public')));

// Check if MONGODB_URI is set
if (!process.env.MONGODB_URI) {
  console.warn("⚠️ MONGODB_URI environment variable is not set. Using default connection string.");
  process.env.MONGODB_URI = "mongodb://localhost:27017/wipdatabase";
}

// Log the MongoDB URI (masked for security)
console.log("MongoDB URI (masked):", 
  process.env.MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, "//****:****@"));

// Middleware
app.use(bodyParser.json());

if (!process.env.SESSION_SECRET && process.env.NODE_ENV === 'production') {
  throw new Error('SESSION_SECRET must be set in production!');
}

// Remove fallback secrets
if (!process.env.JWT_SECRET) {
  throw new Error('JWT_SECRET environment variable is required');
}

const cookieConfig = {
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
  sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
  maxAge: 1000 * 60 * 60 * 24 * 7 // 1 week
};

app.use(
  session({
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    store: MongoStore.create({
      mongoUrl: process.env.MONGODB_URI,
      mongoOptions: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        retryWrites: true,
        w: 'majority'
      },
      collectionName: 'sessions',
      ttl: 14 * 24 * 60 * 60, // = 14 days
      autoRemove: 'native'
    }),
    cookie: cookieConfig
  })
);
app.use(cors({
  origin: [
    'https://pulse.composuregraphics.com',
    'http://localhost:3000'
  ], // Add more domains as needed
  credentials: true
}));

// Ensure these middleware are set up
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add this near the top of your file with other middleware
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Add this near the top of your file
console.log("MongoDB URI (masked):", process.env.MONGODB_URI ? 
  process.env.MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, "//****:****@") : 
  "Not defined");

let isDbConnected = false;

// MongoDB connection with better error handling and retry logic
async function connectToMongoDB() {
  try {
    console.log("🔄 Attempting to connect to MongoDB...");
    
    // Connect with proper options for replica sets
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      // These options help with replica set connections
      retryWrites: true,
      w: 'majority'
    });
    
    console.log("✅ Connected to MongoDB successfully");
    isDbConnected = true;
    
    // Start watching for changes after a delay to ensure connection is fully established
    setTimeout(watchProjectStatusChanges, 3000);
    
    // Set up connection event handlers
    mongoose.connection.on('disconnected', () => {
      console.log('❌ MongoDB disconnected');
      isDbConnected = false;
      // Try to reconnect
      setTimeout(connectToMongoDB, 5000);
    });
    
    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB connection error:', err);
      isDbConnected = false;
    });
    
    return true;
  } catch (err) {
    console.error("❌ MongoDB connection error:", err.message);
    isDbConnected = false;
    
    // Retry connection after 5 seconds
    console.log("🔄 Retrying connection in 5 seconds...");
    setTimeout(connectToMongoDB, 5000);
    
    return false;
  }
}

// Call the connection function
connectToMongoDB();

// Update Cloudinary configuration
try {
  // Log the Cloudinary configuration (with masked secrets)
  console.log("Cloudinary configuration:", {
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'demo',
    api_key: process.env.CLOUDINARY_API_KEY ? '****' : 'not set',
    api_secret: process.env.CLOUDINARY_API_SECRET ? '****' : 'not set'
  });
  
  // Configure Cloudinary with your credentials
  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
  });
  
  // Verify configuration
  const cloudConfig = cloudinary.config();
  if (!cloudConfig.cloud_name || !cloudConfig.api_key || !cloudConfig.api_secret) {
    throw new Error("Missing Cloudinary credentials");
  }
  
  console.log("✅ Cloudinary configured successfully");
} catch (error) {
  console.error("❌ Error configuring Cloudinary:", error);
}

// Set up Cloudinary storage for avatars
const avatarStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'avatars',
    allowed_formats: ['jpg', 'jpeg', 'png', 'gif'],
    transformation: [{ width: 200, height: 200, crop: 'limit' }]
  }
});

// Set up Cloudinary storage for project files
const projectFileStorage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'project-files',
    allowed_formats: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
    resource_type: 'auto'
  }
});

// Update multer configurations with error handling
const avatarUpload = multer({ 
  storage: avatarStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    try {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.mimetype)) {
        return cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'), false);
      }
      cb(null, true);
    } catch (err) {
      cb(new Error('File processing error'), false);
    }
  }
});

const projectAllowedTypes = [
  'image/jpeg', 'image/png', 'application/pdf',
  'application/msword', // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.ms-excel', // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // .xlsx
];

const projectFileUpload = multer({ 
  storage: projectFileStorage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    try {
      if (!projectAllowedTypes.includes(file.mimetype)) {
        return cb(new Error('Invalid file type. Only images, PDF, Word, and Excel files are allowed.'), false);
      }
      cb(null, true);
    } catch (err) {
      cb(new Error('File processing error'), false);
    }
  }
});

// Update CSV upload with error handling
const csvUpload = multer({ 
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      try {
        const uploadDir = path.join(__dirname, 'temp-uploads');
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
      } catch (err) {
        cb(new Error('Error creating upload directory'), false);
      }
    },
    filename: (req, file, cb) => {
      try {
        cb(null, `bulk-users-${Date.now()}-${file.originalname}`);
      } catch (err) {
        cb(new Error('Error generating filename'), false);
      }
    }
  }),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    try {
      // Validate file is a CSV
      if (file.mimetype !== 'text/csv' && !file.originalname.endsWith('.csv')) {
        return cb(new Error('Only CSV files are allowed'), false);
      }
      cb(null, true);
    } catch (err) {
      cb(new Error('File processing error'), false);
    }
  }
});

// Add global error handler for multer
app.use((err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    // A Multer error occurred when uploading
    console.error('Multer error:', err);
    return res.status(400).json({
      message: 'File upload error',
      error: err.message
    });
  } else if (err) {
    // An unknown error occurred
    console.error('Unknown error:', err);
    return res.status(500).json({
      message: 'Server error during file upload',
      error: err.message
    });
  }
  next();
});

// Add this middleware to check DB connection before processing API requests
app.use('/api', async (req, res, next) => {
  console.log(`API Request: ${req.method} ${req.url}`);
  
  // Check if database is connected
  if (!isDbConnected) {
    console.log("⚠️ Database not connected, checking connection state...");
    
    // Check connection state
    if (mongoose.connection.readyState !== 1) {
      console.log("⚠️ Attempting to reconnect to database...");
      
      try {
        // Try to reconnect
        const connected = await connectToMongoDB();
        if (!connected) {
          return res.status(503).json({ 
            message: "Database connection unavailable, please try again later",
            readyState: mongoose.connection.readyState
          });
        }
      } catch (error) {
        console.error("❌ Failed to reconnect to database:", error);
        return res.status(503).json({ 
          message: "Database connection unavailable, please try again later" 
        });
      }
    } else {
      isDbConnected = true;
      console.log("✅ Database connection is actually active");
    }
  }
  
  next();
});

// Add this function to get Cloudinary URLs for static assets
function getCloudinaryUrl(assetName) {
  // Check if Cloudinary is configured
  const config = cloudinary.config();
  if (!config.cloud_name || !config.api_key || !config.api_secret) {
    // Return local path if Cloudinary is not configured
    return assetName;
  }
  
  // Map of asset names to their specific public IDs
  const assetPublicIds = {
    'success.png': 'success_tayqq4',
    'addproject.png': 'addproject_gs1mly' // Add the public ID after uploading
  };
  
  // Use the specific public ID if available, otherwise use the asset name
  const publicId = assetPublicIds[assetName] || assetName;
  
  // Return the Cloudinary URL for the asset
  return cloudinary.url(publicId, {
    secure: true
  });
}

// Add this endpoint to serve static assets from Cloudinary
app.get('/api/asset-url/:assetName', (req, res) => {
  try {
    const { assetName } = req.params;
    const url = getCloudinaryUrl(assetName);
    console.log(`Generated Cloudinary URL for ${assetName}: ${url}`);
    res.json({ url });
  } catch (error) {
    console.error(`Error generating asset URL for ${req.params.assetName}:`, error);
    res.status(500).json({ 
      url: req.params.assetName,
      error: error.message
    });
  }
});

// Remove all schema and model definitions from server.js
// And add this import at the top of your file
const {
  User,
  Project,
  YetToStartProject,
  InProgressProject,
  CompletedProject,
  ProjectStatus,
  ProjectWIPweightage,
  ApprovalStatus,
  File,
  Ticket,
  ProjectCumulativeWIP,
  ProjectStatusSummary,
  ProjectWIP,
  WeightedProjectWIPSummary,
  RoleUser
} = require('./models');

// Import the AuditLog model
const AuditLog = require('./models/AuditLog');

// Authentication middleware
function isAuthenticated(req, res, next) {
  const token = req.headers["authorization"]?.split(" ")[1]; // Extract token
  console.log("Token received:", token); // Debugging

  if (!token) {
    console.log("No token provided");
    return res.status(401).json({ message: "Unauthorized access" });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      console.log("Token verification failed:", err);
      return res.status(403).json({ message: "Forbidden access" });
    }
    console.log("Authenticated user:", user); // Debugging
    req.user = user;
    next();
  });
}

// Add this near your other routes
app.get('/', (req, res) => {
  // Redirect root URL to login page
  res.redirect('/login.html');
});

// Serve login.html publicly
app.get("/login.html", (req, res) => {
  res.sendFile(path.join(__dirname, "login.html"));
});

app.get(
  "/api/inprogressprojects/:projectId",
  isAuthenticated,
  authorizeRoles(["Admin", "Project Manager", "Creative Service", "Editorial"]),
  async (req, res) => {
    const { projectId } = req.params;

    try {
      console.log("Fetching project details for projectId:", projectId); // Debugging
      const project = await Project.findById(projectId); // Use `findById` to fetch the project by its `_id`
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }
      res.status(200).json(project);
    } catch (error) {
      console.error("Error fetching project details:", error);
      res.status(500).json({ message: "Error fetching project details" });
    }
  }
);

// Serve new-project.html
app.get("/new-project.html", (req, res) => {
  res.sendFile(path.join(__dirname, "new-project.html"));
});

app.get("/project-status.html", (req, res) => {
  res.sendFile(path.join(__dirname, "project-status.html"));
});

// Serve static files from the root directory and subdirectories
app.use(express.static(path.join(__dirname, '')));
app.use('/styles', express.static(path.join(__dirname, 'styles')));
app.use('/images', express.static(path.join(__dirname, 'images')));

// Routes
app.post("/api/register", [
  body('empId').notEmpty().withMessage('Employee ID is required').trim().escape(),
  body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters').trim().escape(),
  body('email').isEmail().withMessage('Invalid email').normalizeEmail(),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('dob').isISO8601().withMessage('Invalid date of birth'),
  body('role').isIn(['Admin', 'Project Manager', 'Creative Service', 'Editorial', 'Basic']).withMessage('Invalid role'),
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  const { 
    empId,
    username, 
    email, 
    password, 
    dob, 
    role,
    phone,
    location,
    department,
    position,
    joinDate,
    avatarUrl: defaultAvatarUrl // Use Cloudinary default avatar
  } = req.body;

  try {
    // Validate required fields
    if (!empId || !username || !email || !password || !dob || !role) {
      return res.status(400).json({ message: 'Required fields missing' });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ 
      $or: [
        { username: username },
        { email: email },
        { empId: empId }
      ]
    });

    if (existingUser) {
      return res.status(400).json({ 
        message: 'Username, email, or Employee ID already exists' 
      });
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user with all fields
    const newUser = new User({
      empId,
      username,
      email,
      password: hashedPassword,
      dob: new Date(dob),
      role,
      phone: phone || undefined,
      location: location || undefined,
      department: department || undefined,
      position: position || undefined,
      joinDate: joinDate ? new Date(joinDate) : undefined
    });

    await newUser.save();

    // Create audit log for user registration
    // Since this is a registration endpoint, req.user might not exist
    // We'll use the admin user or the user being created as the performer
    const auditReq = {
      user: req.user || { 
        id: newUser._id, 
        username: newUser.username 
      }
    };
    
    await createAuditLog(
      auditReq,
      'CREATE',
      'user',
      newUser._id.toString(),
      `User ${username} registered with role ${role}`,
      {
        before: null,
        after: {
          empId,
          username,
          email,
          role,
          dob: new Date(dob),
          phone: phone || undefined,
          location: location || undefined,
          department: department || undefined,
          position: position || undefined,
          joinDate: joinDate ? new Date(joinDate) : undefined
          // Exclude password for security
        }
      }
    );

    // Send welcome email with credentials
    try {
      await sendWelcomeEmail(email, username, password, role);
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
      // Continue with registration even if email fails
    }

    // Send success response without password
    const userResponse = newUser.toObject();
    delete userResponse.password;
    
    res.status(201).json({
      message: 'User registered successfully',
      user: userResponse
    });

  } catch (error) {
    console.error('Error registering user:', error);
    res.status(500).json({ 
      message: 'Error registering user',
      error: error.message 
    });
  }
});

// Rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: "Too many login attempts from this IP, please try again after 15 minutes"
});

app.use('/api/login', authLimiter);
app.use('/api/register', authLimiter);

app.post("/api/login", async (req, res) => {
  try {
    console.log("Login attempt for username:", req.body.username);
    const { username, password } = req.body;
    
    // Check if username is provided
    if (!username) {
      console.log("Login failed: No username provided");
      return res.status(400).json({ message: "Username is required" });
    }
    
    // Find the user
    const user = await User.findOne({ username });
    
    if (!user) {
      console.log("Login failed: User not found for username:", username);
      return res.status(401).json({ message: "Invalid credentials" });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      console.log("Login failed: Invalid password for username:", username);
      return res.status(401).json({ message: "Invalid credentials" });
    }

    console.log("Login successful for username:", username);
    
    // Update last active timestamp
    user.lastActive = new Date();
    await user.save();

    // Create token with user ID, username and role
    const token = jwt.sign(
      { id: user._id, role: user.role, username: user.username }, // add username here
      process.env.JWT_SECRET,
      { expiresIn: "1h" }
    );

    // Keep the same response structure as before
    res.status(200).json({ 
      message: "Login successful", 
      role: user.role, 
      token, 
      name: user.username,
      id: user._id,
      avatarUrl: user.avatarUrl || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

app.post("/api/logout", (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).send("Failed to log out");
    }
    res.sendStatus(200);
  });
});

// Add this route to serve your HTML files
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Add routes for other HTML files
app.get('/login', (req, res) => {
  res.sendFile(path.join(__dirname, 'login.html'));
});

// Add a specific route for Invoices.html with role-based authorization
app.get('/Invoices.html', isAuthenticated, (req, res, next) => {
  const userRole = req.user.role;
  
  if (userRole === 'Admin' || userRole === 'Project Manager') {
    // User is authorized, serve the file
    res.sendFile(path.join(__dirname, 'Invoices.html'));
  } else {
    // User is not authorized, redirect to access denied or home page
    res.status(403).send(`
      <script>
        alert('Access denied. Only Administrators and Project Managers can access this page.');
        window.location.href = '/index.html';
      </script>
    `);
  }
});

// This should come AFTER the specific route for Invoices.html
// Add a catch-all route for other HTML files
app.get('/:page.html', (req, res) => {
  const page = req.params.page;
  res.sendFile(path.join(__dirname, `${page}.html`));
});

// API Routes (Protected)
// Admin: Full access
app.get("/api/inprogressprojects", isAuthenticated, authorizeRoles(["Admin", "Project Manager", "Creative Service", "Editorial", "Basic"]), async (req, res) => {
  try {
    const projects = await Project.find();
    res.status(200).json(projects);
  } catch (error) {
    console.error("Error fetching projects:", error);
    res.status(500).json({ message: "Error fetching projects" });
  }
});

// Add project: Admin and Project Manager
app.post("/api/yettostartprojects", [
  isAuthenticated,
  authorizeRoles(["Admin", "Project Manager"]),
  body('projectId').notEmpty().withMessage('Project ID is required').trim().escape(),
  body('levels').isInt({ min: 1 }).withMessage('Levels must be a positive integer'),
  body('units').isInt({ min: 1 }).withMessage('Units must be a positive integer'),
  body('title').optional().trim().escape(),
  body('clientName').optional().trim().escape(),
  // Add more fields as needed
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  console.log("📥 Received project creation request:", req.body);
  
  const { projectId, levels, units, parentProjectId, supportingTeam, notes, isbns, ...projectData } = req.body;

  // Log the extracted data
  console.log("Extracted data:", { 
    projectId, 
    levels, 
    units, 
    parentProjectId, 
    supportingTeam: Array.isArray(supportingTeam) ? `Array with ${supportingTeam.length} items` : supportingTeam,
    isbns: Array.isArray(isbns) ? `Array with ${isbns.length} ISBNs` : isbns,
    notes: notes ? `${notes.substring(0, 20)}...` : null,
    otherData: Object.keys(projectData)
  });

  if (!projectId) {
    console.error(`🚨 Error: Project ID is missing!`);
    return res.status(400).json("Project ID is required");
  }

  // Start a database session for transaction
  const session = await mongoose.startSession();
  
  try {
    await session.withTransaction(async () => {
    // ✅ Ensure project does not already exist
      const existingProject = await YetToStartProject.findById(projectId).session(session);
    if (existingProject) {
      console.error(`🚨 Error: Project ID ${projectId} already exists!`);
        throw new Error("Project ID already exists");
    }

    // ✅ Convert levels and units to numbers and validate
    const numLevels = parseInt(levels, 10);
    const numUnits = parseInt(units, 10);
    console.log("Parsed levels and units:", { numLevels, numUnits });
    
    if (isNaN(numLevels) || numLevels <= 0) {
      console.error(`🚨 Error: Invalid levels value: ${levels}`);
        throw new Error("Invalid levels value");
    }
    if (isNaN(numUnits) || numUnits <= 0) {
      console.error(`🚨 Error: Invalid units value: ${units}`);
        throw new Error("Invalid units value");
    }

    // ✅ Create Project with string-based ID
    const project = new YetToStartProject({
      _id: projectId,
      levels: numLevels,
      units: numUnits,
        parentProjectId: parentProjectId || projectId,
        supportingTeam: supportingTeam || [],
        notes: notes || "",
        isbns: isbns || [],
      ...projectData,
    });
    
      await project.save({ session });
    console.log("✅ Project created successfully in yettostartprojects:", project);

    // Create audit log for project creation
    await createAuditLog(
      req,
      'CREATE',
      'project',
      projectId,
      `Project ${projectData.title || projectId} created`,
      {
        before: null,
        after: project.toObject()
      }
    );

    // ✅ Create multiple ProjectStatus entries based on levels
    for (let level = 1; level <= numLevels; level++) {
      const statusRows = [];
      for (let unit = 1; unit <= numUnits; unit++) {
        statusRows.push([`Unit ${unit}`, "Yet to Start"]);
      }

      const projectStatus = new ProjectStatus({
          projectId: `${projectId}-level-${level}`,
        statusData: {
          headers: [`Level ${level} Heading`, "Column 1"],
          rows: statusRows,
        },
      });

        await projectStatus.save({ session });
        console.log(`✅ Project status entry created for Level ${level}:`, projectStatus);
    }

    // ✅ Insert default projectwipweightages values for all levels
    for (let level = 1; level <= numLevels; level++) {
      const defaultWeightage = {
          projectId: `${projectId}-level-${level}`,
          weightages: [100],
      };

      const projectWIPWeightage = new ProjectWIPweightage(defaultWeightage);
        await projectWIPWeightage.save({ session });
        console.log(`✅ Default projectwipweightages entry created for Level ${level}:`, projectWIPWeightage);
    }
    });

    // If we reach here, the transaction was successful
    res.status(201).send({ message: "Project created successfully" });
    
  } catch (error) {
    console.error("❌ Error in project creation transaction:", error);
    // Check for known validation errors and MongoDB duplicate key error in project or status
    if (
      error.message === "Project ID already exists" ||
      error.code === 11000 ||
      (error.errmsg && error.errmsg.includes('projectstatuses index: projectId_1'))
    ) {
      return res.status(400).json({ error: "Project ID already exists" });
    } else if (error.message === "Invalid levels value") {
      return res.status(400).json({ error: "Invalid levels value" });
    } else if (error.message === "Invalid units value") {
      return res.status(400).json({ error: "Invalid units value" });
    } else {
      return res.status(500).json({ error: "Error creating project" });
    }
  } finally {
    await session.endSession();
  }
});

// Edit project: Admin and Project Manager
app.put("/api/inprogressproject/:id", isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), async (req, res) => {
  try {
    const { parentProjectId } = req.body;
    const projectId = req.params.id;

    // If parentProjectId is provided and different from the project's own ID
    if (parentProjectId && parentProjectId !== projectId) {
      // Check if the parent project exists in any of the collections
      const parentInYetToStart = await YetToStartProject.findById(parentProjectId);
      const parentInProgress = await InProgressProject.findById(parentProjectId);
      const parentCompleted = await CompletedProject.findById(parentProjectId);

      // If parent project doesn't exist in any collection
      if (!parentInYetToStart && !parentInProgress && !parentCompleted) {
        return res.status(400).json("Parent project is not available");
      }
    }

    // Continue with the update if validation passes
    const projectBefore = await Project.findById(projectId).lean();
    if (!projectBefore) {
      return res.status(404).json("Project not found");
    }

    const project = await Project.findByIdAndUpdate(projectId, req.body, {
      new: true,
    });

    // Create audit log for project update
    await createAuditLog(
      req,
      'UPDATE',
      'project',
      projectId,
      `Project ${projectBefore.projectName} updated`,
      {
        before: projectBefore,
        after: project.toObject()
      }
    );

    res.status(200).send(project);
  } catch (error) {
    console.error("❌ Error updating project:", error);
    res.status(400).json("Error updating project");
  }
});

// Edit yet to start project: Admin and Project Manager
app.put("/api/yettostartprojects/:id", isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), async (req, res) => {
  try {
    const { parentProjectId } = req.body;
    const projectId = req.params.id;

    // Validate project ID
    if (!projectId) {
      return res.status(400).json("Project ID is required");
    }

    // Continue with the update if validation passes
    const projectBefore = await YetToStartProject.findById(projectId).lean();
    if (!projectBefore) {
      return res.status(404).json("Project not found");
    }

    const project = await YetToStartProject.findByIdAndUpdate(projectId, req.body, {
      new: true,
    });

    // Create audit log for project update
    await createAuditLog(
      req,
      'UPDATE',
      'project',
      projectId,
      `Project ${projectBefore.title || projectId} updated`,
      {
        before: projectBefore,
        after: project.toObject()
      }
    );

    res.status(200).send(project);
  } catch (error) {
    console.error("❌ Error updating project:", error);
    res.status(400).json("Error updating project");
  }
});

// Delete project: Admin only
app.delete("/api/inprogressprojects/:id", isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
    const projectId = req.params.id;

    // Attempt to delete from all possible project collections
    const deletedInProgress = await InProgressProject.findByIdAndDelete(projectId);
    const deletedYetToStart = await YetToStartProject.findByIdAndDelete(projectId);
    const deletedCompleted = await CompletedProject.findByIdAndDelete(projectId);

    const deletedProject = deletedInProgress || deletedYetToStart || deletedCompleted;

    if (!deletedProject) {
      return res.status(404).send("Project not found in any collection");
    }

    // Delete all related project statuses from the ProjectStatus collection
    const deletedStatuses = await ProjectStatus.deleteMany({
      projectId: new RegExp(`^${projectId}`),
    });
    console.log(
      `✅ Deleted ${deletedStatuses.deletedCount} project status entries for projectId: ${projectId}`
    );

    const deletedwipweightages = await ProjectWIPweightage.deleteMany({
      projectId: new RegExp(`^${projectId}`),
    });
    console.log(
      `✅ Deleted ${deletedwipweightages.deletedCount} project status entries for projectId: ${projectId}`
    );

    // Delete related data from the projectStatusSummary collection
    const deletedSummary = await mongoose.connection.db
      .collection("projectStatusSummary")
      .deleteMany({ projectId: new RegExp(`^${projectId}`) });
    console.log(
      `✅ Deleted ${deletedSummary.deletedCount} summary entries for projectId: ${projectId}`
    );

    // Delete related data from the projectwip collection
    const deletedWIP = await mongoose.connection.db
      .collection("projectwip")
      .deleteMany({ projectId: new RegExp(`^${projectId}`) });
    console.log(
      `✅ Deleted ${deletedWIP.deletedCount} WIP entries for projectId: ${projectId}`
    );

    // Delete related data from the weightedProjectwipSummaries collection
    const deletedWeightedWIP = await mongoose.connection.db
      .collection("weightedProjectwipSummaries")
      .deleteMany({ projectId: new RegExp(`^${projectId}`) });
    console.log(
      `✅ Deleted ${deletedWeightedWIP.deletedCount} weighted WIP entries for projectId: ${projectId}`
    );

    // Delete related data from the projectCumulativeWIP collection
    const deletedCumulativeWIP = await ProjectCumulativeWIP.deleteOne({ 
      projectId: projectId 
    });
    console.log(
      `✅ Deleted ${deletedCumulativeWIP.deletedCount} cumulative WIP entries for projectId: ${projectId}`
    );

    // Create audit log for project deletion
    await createAuditLog(
      req,
      'DELETE',
      'project',
      projectId,
      `Project ${deletedProject.title || projectId} deleted`,
      {
        before: deletedProject.toObject(),
        after: null
      }
    );

    res.status(200).send(
      "Project and all related data (statuses, summaries, WIP, weighted WIP, cumulative WIP) deleted successfully"
    );
  } catch (error) {
    console.error("❌ Error deleting project and related data:", error);
    res.status(500).send("Error deleting project and related data");
  }
});

// API endpoints for project status
app.post("/api/project-status", isAuthenticated, async (req, res) => {
  try {
    let { projectId, statusData } = req.body;

    if (!projectId) {
      return res.status(400).json("Error: No project selected.");
    }

    // Store projectId as a string
    const updatedStatus = await ProjectStatus.findOneAndUpdate(
      { projectId: projectId },
      { statusData },
      { new: true, upsert: true }
    );

    res.status(200).send("Project status saved successfully");
  } catch (error) {
    console.error("Error saving project status:", error);
    res.status(500).send("Error saving project status");
  }
});

app.post("/api/project-status/save", isAuthenticated, async (req, res) => {
  const { projectId, level, headers, rows } = req.body;

  try {
    // Log incoming data
    console.log("📥 Received data:", { projectId, level, headers, rows });

    // Validate input
    if (!projectId || level === undefined || !headers || !rows) {
      console.error("❌ Missing required fields:", { projectId, level, headers, rows });
      return res.status(400).json({
        message: "Missing required fields",
        received: { projectId, level, headers, rows }
      });
    }

    const levelId = `${projectId}-level-${level}`;
    console.log("🔍 Generated levelId:", levelId);

    // Create or update the project status
    const updatedStatus = await ProjectStatus.findOneAndUpdate(
      { projectId: levelId },
      { 
        projectId: levelId,
        statusData: { 
          headers: headers,
          rows: rows
        }
      },
      { 
        new: true,
        upsert: true,
        runValidators: true
      }
    );

    console.log("✅ Saved Project Status:", updatedStatus);
    
    res.status(200).json({
      message: "Project status saved successfully",
      data: updatedStatus
    });
  } catch (error) {
    console.error("❌ Error saving project status:", error);
    res.status(500).json({ 
      message: "Error saving project status",
      error: error.message
    });
  }
});

app.post(
  "/api/project-wip-weightage/save",
  isAuthenticated,
  async (req, res) => {
    const { projectId, level, weightages } = req.body;

    try {
      // Log the incoming data for debugging
      console.log("🔍 Received WIP Weightage Data:", {
        projectId,
        level,
        weightages,
      });

      // Validate the projectId format
      if (!projectId || !projectId.includes(`-level-${level}`)) {
        console.error("❌ Invalid projectId or level format");
        return res.status(400).json("Invalid projectId or level format");
      }

      // Save the data to the database
      const updatedWeightage = await ProjectWIPweightage.findOneAndUpdate(
        { projectId },
        { $set: { weightages } }, // Use $set to update the weightages field
        { new: true, upsert: true }
      );

      console.log("✅ Updated WIP Weightage:", updatedWeightage);
      res.status(200).send("WIP Weightage saved successfully");
    } catch (error) {
      console.error("❌ Error saving WIP Weightage:", error.message); // Log the error message
      res.status(500).send("Error saving WIP Weightage");
    }
  }
);

app.get(
  "/api/project-wip-weightage/:projectId",
  isAuthenticated,
  async (req, res) => {
    const { projectId } = req.params;

    try {
      // Fetch the existing weightages from the database
      const weightageData = await ProjectWIPweightage.findOne({ projectId });
      if (weightageData) {
        console.log("✅ Fetched WIP Weightage:", weightageData);
        res.status(200).json(weightageData);
      } else {
        console.warn("⚠️ No WIP Weightage found for projectId:", projectId);
        res.status(404).send("No WIP Weightage found");
      }
    } catch (error) {
      console.error("❌ Error fetching WIP Weightage:", error.message);
      res.status(500).send("Error fetching WIP Weightage");
    }
  }
);

app.get('/api/project-wip/:projectId', isAuthenticated, async (req, res) => {
    const { projectId } = req.params;

    try {
        console.log(`Fetching WIP data for projectId: ${projectId}`);

        // Use mongoose.connection.db instead of db
        const projectWIPData = await mongoose.connection.db.collection("projectwip").find({ 
            projectId: new RegExp(`^${projectId}-level-`, 'i') 
        }).toArray();

        if (projectWIPData.length === 0) {
            console.error(`❌ No WIP data found for projectId: ${projectId}`);
            return res.status(404).send('WIP data not found');
        }

        res.status(200).json(projectWIPData);
    } catch (error) {
        console.error('❌ Error fetching WIP data:', error);
        res.status(500).send('Error fetching WIP data');
    }
});

// Utility function to safely escape special characters in regex
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // Escape special regex characters
}

// Project status: Accessible to all except Basic
app.get("/api/project-status/:projectId", isAuthenticated, authorizeRoles(["Admin", "Project Manager", "Creative Service", "Editorial"]), async (req, res) => {
  const { projectId } = req.params;
  const escapedProjectId = escapeRegExp(projectId); // Escape user input

  try {
    // Use MongoDB regex to find statuses linked to projectId-level-x
    const projectStatuses = await ProjectStatus.find({
      projectId: { $regex: `^${escapedProjectId}-level-`, $options: "i" },
    });

    if (projectStatuses.length > 0) {
      // Include the projectId in each status object
      const statusesWithIds = projectStatuses.map((status) => ({
        projectId: status.projectId,
        headers: status.statusData.headers,
        rows: status.statusData.rows
      }));
      
      res.status(200).json(statusesWithIds);
    } else {
      console.error(`❌ Project status not found for projectId: ${projectId}`);
      res.status(404).send("Project status not found");
    }
  } catch (error) {
    console.error("❌ Error retrieving project status:", error);
    res.status(500).send("Error retrieving project status");
  }
});

app.post("/api/project-status/update-header", isAuthenticated, async (req, res) => {
  const { projectId, headerIndex, newText } = req.body;

  try {
    const projectStatus = await ProjectStatus.findOne({ projectId });
    if (!projectStatus) {
      return res.status(404).send("Project status not found");
    }

    // Update the header
    projectStatus.statusData.headers[headerIndex] = newText;
    await projectStatus.save();

    res.status(200).send("Header updated successfully");
  } catch (error) {
    console.error("Error updating header:", error);
    res.status(500).send("Error updating header");
  }
});

app.get('/project-count', isAuthenticated, async (req, res) => {
  try {
    // Check if database is connected
    if (!isDbConnected) {
      console.log("⚠️ Database not connected, waiting for connection...");
      const connected = await connectToMongoDB();
      if (!connected) {
        return res.status(503).json({ 
          message: "Database connection unavailable, please try again later" 
        });
      }
    }

    // Use the Mongoose model instead of direct db access
    const count = await YetToStartProject.countDocuments();
    res.json({ count });
  } catch (err) {
    console.error('Error fetching project count:', err);
    res.status(500).json({ error: 'Failed to fetch project count' });
  }
});

// Define the route
app.get("/api/weighted-project-wip", isAuthenticated, async (req, res) => {
  try {
    console.log("Fetching data from weightedProjectwipSummaries..."); // Debugging

    const data = await mongoose.connection.db
      .collection("weightedProjectwipSummaries")
      .find({})
      .toArray();

    console.log("Data fetched:", data); // Debugging

    if (!data || data.length === 0) {
      return res.status(404).json({ message: "No data found" });
    }

    res.status(200).json(data);
  } catch (error) {
    console.error("Error fetching weighted WIP data:", error);
    res.status(500).json({ message: "Error fetching weighted WIP data" });
  }
});

// Serve static files
app.use(express.static(path.join(__dirname)));

app.use(helmet());

app.use(router);
 
app.get('/api/approval-status/:projectId', isAuthenticated, async (req, res) => {
  const { projectId } = req.params;
 
  try {
    const approvalStatus = await ApprovalStatus.findOne({ projectId });
    if (!approvalStatus) {
      return res.status(404).json({ message: "Approval status not found" });
    }
 
    // Define consistent order
    const STAGE_ORDER = [
      "Reconcilation completed by PM",
      "Reconcilation approved by Peter",
      "Invoice Sent to Client",
      "Invoice Paid",
      "Project Archived"
    ];
 
    // Sort the stages array based on the fixed order
    approvalStatus.stages.sort((a, b) => {
      return STAGE_ORDER.indexOf(a.stage) - STAGE_ORDER.indexOf(b.stage);
    });
 
    res.status(200).json(approvalStatus);
  } catch (error) {
    console.error("❌ Error fetching approval status:", error);
    res.status(500).json({ message: "Error fetching approval status" });
  }
});

// Update a specific stage's status with audit logging
app.put("/api/approval-status/:projectId/stage/:stageIndex", isAuthenticated, async (req, res) => {
  try {
    const { projectId, stageIndex } = req.params;
    const { approvedBy } = req.body;

    const approvalStatus = await ApprovalStatus.findOne({ projectId });
    if (!approvalStatus) {
      return res.status(404).json({ message: "Approval status not found" });
    }

    const stage = approvalStatus.stages[stageIndex];
    if (!stage) {
      return res.status(404).json({ message: "Stage not found" });
    }

    // Store the stage state before update for audit logging
    const stageBefore = {
      stage: stage.stage,
      completed: stage.completed,
      approvedBy: stage.approvedBy,
      approvedAt: stage.approvedAt
    };

    // Update the stage
    stage.completed = true;
    stage.approvedBy = approvedBy;
    stage.approvedAt = new Date();

    await approvalStatus.save();

    // Create audit log for stage approval
    await createAuditLog(
      req,
      'UPDATE',
      'approvalStatus',
      projectId,
      `Approval stage "${stage.stage}" marked as completed`,
      {
        before: stageBefore,
        after: {
          stage: stage.stage,
          completed: stage.completed,
          approvedBy: stage.approvedBy,
          approvedAt: stage.approvedAt
        }
      }
    );

    res.status(200).json({ message: "Stage updated successfully", approvalStatus });
  } catch (error) {
    console.error("❌ Error updating stage:", error);
    res.status(500).json({ message: "Error updating stage" });
  }
});

// File upload endpoint with audit logging
app.post("/api/approval-status/:projectId/stage/:stageIndex/upload", isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), projectFileUpload.single("file"), async (req, res) => {
  try {
    const { projectId, stageIndex } = req.params;

    if (!req.file) {
      return res.status(400).json({ message: "No file uploaded" });
    }

    // Save file metadata to MongoDB
    const file = new File({
      projectId,
      stageIndex: parseInt(stageIndex, 10),
      filename: req.file.filename || req.file.originalname,
      originalName: req.file.originalname,
      fileUrl: req.file.path, // Store Cloudinary URL
      uploadDate: new Date()
    });

    await file.save();

    // Get the stage name for better audit logging
    const approvalStatus = await ApprovalStatus.findOne({ projectId });
    const stageName = approvalStatus && approvalStatus.stages[stageIndex] 
      ? approvalStatus.stages[stageIndex].stage 
      : `Stage ${stageIndex}`;

    // Create audit log for file upload
    await createAuditLog(
      req,
      'CREATE',
      'file',
      `${projectId}-${stageIndex}-${file._id}`,
      `File "${req.file.originalname}" uploaded for approval stage "${stageName}"`,
      {
        before: null,
        after: {
          projectId,
          stageIndex: parseInt(stageIndex, 10),
          filename: req.file.filename || req.file.originalname,
          originalName: req.file.originalname,
          fileUrl: req.file.path,
          uploadDate: new Date()
        }
      }
    );

    console.log(`✅ File uploaded successfully: ${req.file.originalname}`);
    res.status(200).json({ 
      message: "File uploaded successfully", 
      file: {
        ...file.toObject(),
        fileUrl: req.file.path // Include the Cloudinary URL
      }
    });
  } catch (error) {
    console.error("❌ Error uploading file:", error);
    res.status(500).json({ message: "Error uploading file", error: error.message });
  }
});

// Endpoint to fetch files for a specific stage
app.get("/api/approval-status/:projectId/stage/:stageIndex/files", async (req, res) => {
  try {
    const { projectId, stageIndex } = req.params;

    const files = await File.find({ projectId, stageIndex: parseInt(stageIndex, 10) });
    res.status(200).json(files);
  } catch (error) {
    console.error("❌ Error fetching files:", error);
    res.status(500).json({ message: "Error fetching files" });
  }
});

// Create initial approval status with audit logging
app.post("/api/approval-status/initialize/:projectId", isAuthenticated, async (req, res) => {
  try {
    const { projectId } = req.params;
    const { projectManager } = req.body;

    // Check if approval status already exists
    const existingStatus = await ApprovalStatus.findOne({ projectId });
    if (existingStatus) {
      return res.status(400).json({ message: "Approval status already exists for this project" });
    }

    // Initialize approval stages
    const approvalStages = [
      { stage: "Reconcilation completed by PM", completed: false, buttonVisibleTo: projectManager || "Project Manager" },
      { stage: "Reconcilation approved by Peter", completed: false, buttonVisibleTo: "Peter" },
      { stage: "Invoice Sent to Client", completed: false, buttonVisibleTo: "Admin" },
      { stage: "Invoice Paid", completed: false, buttonVisibleTo: "Admin" },
      { stage: "Project Archived", completed: false, buttonVisibleTo: "Admin" }
    ];

    const approvalStatus = new ApprovalStatus({
      projectId,
      stages: approvalStages,
    });

    await approvalStatus.save();

    // Create audit log for initializing approval status
    await createAuditLog(
      req,
      'CREATE',
      'approvalStatus',
      projectId,
      `Approval status initialized for project ${projectId}`,
      {
        before: null,
        after: approvalStatus.toObject()
      }
    );

    res.status(201).json({ 
      message: "Approval status initialized successfully", 
      approvalStatus 
    });
  } catch (error) {
    console.error("❌ Error initializing approval status:", error);
    res.status(500).json({ message: "Error initializing approval status" });
  }
});

// Reset a stage (mark as incomplete) with audit logging
app.put("/api/approval-status/:projectId/stage/:stageIndex/reset", isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
    const { projectId, stageIndex } = req.params;
    
    const approvalStatus = await ApprovalStatus.findOne({ projectId });
    if (!approvalStatus) {
      return res.status(404).json({ message: "Approval status not found" });
    }

    const stage = approvalStatus.stages[stageIndex];
    if (!stage) {
      return res.status(404).json({ message: "Stage not found" });
    }

    // Store the stage state before update for audit logging
    const stageBefore = {
      stage: stage.stage,
      completed: stage.completed,
      approvedBy: stage.approvedBy,
      approvedAt: stage.approvedAt
    };

    // Reset the stage
    stage.completed = false;
    stage.approvedBy = null;
    stage.approvedAt = null;

    await approvalStatus.save();

    // Create audit log for stage reset
    await createAuditLog(
      req,
      'UPDATE',
      'approvalStatus',
      projectId,
      `Approval stage "${stage.stage}" reset to incomplete`,
      {
        before: stageBefore,
        after: {
          stage: stage.stage,
          completed: stage.completed,
          approvedBy: stage.approvedBy,
          approvedAt: stage.approvedAt
        }
      }
    );

    res.status(200).json({ 
      message: "Stage reset successfully", 
      approvalStatus 
    });
  } catch (error) {
    console.error("Error resetting stage:", error);
    res.status(500).json({ message: "Error resetting stage" });
  }
});

// API Routes for Tickets
// Create new ticket
app.post("/api/tickets", isAuthenticated, async (req, res) => {
    try {
        const {
            projectId,
            customerCode,
            projectName,
            ticketHeading,
            ticketType,
            status,
            dueDate,
            projectManager,
            assignedTo,
            supportingTeam,
            instructions,
            comments // Add this field
        } = req.body;

        // Validate ticket type
        if (!['First', 'Cust Alt', 'Rework'].includes(ticketType)) {
            return res.status(400).json({ message: "Invalid ticket type" });
        }

        // Generate ticket number based on the last created ticket
        const latestTicket = await Ticket.findOne().sort({ ticketNumber: -1 });
        let nextNumber = 1;

        if (latestTicket && latestTicket.ticketNumber) {
            const lastNumber = parseInt(latestTicket.ticketNumber.split('-').pop(), 10);
            if (!isNaN(lastNumber)) {
                nextNumber = lastNumber + 1;
            }
        }
        
        const currentYear = new Date().getFullYear();
        const ticketNumber = `TKT-${currentYear}-${nextNumber.toString().padStart(4, '0')}`;

        const ticket = new Ticket({
            ticketNumber,
            projectId,
            customerCode,
            projectName,
            ticketHeading,
            ticketType,
            status,
            dueDate,
            projectManager,
            assignedTo,
            supportingTeam,
            instructions,
            comments // Add this field
        });

        await ticket.save();

        // Create audit log using the ticket's string ticketNumber as entityId
        await createAuditLog(
            req,
            'CREATE',
            'ticket',
            ticket.ticketNumber, // Use the string ticketNumber as entityId
            `Ticket ${ticket.ticketNumber} created`,
            {
                before: null,
                after: ticket.toObject()
            }
        );

        res.status(201).json(ticket);
    } catch (error) {
        console.error('Error creating ticket:', error);
        res.status(500).json({ message: "Error creating ticket", error: error.message });
    }
}); // Create new ticket route

// Get all tickets
app.get("/api/tickets", isAuthenticated, async (req, res) => {
    try {
        const tickets = await Ticket.find({ status: { $ne: 'deleted' } })
            .select('ticketNumber projectId customerCode projectName ticketHeading ticketType status createdDate dueDate projectManager assignedTo supportingTeam instructions comments') // Added supportingTeam
            .sort({ createdDate: -1 });
        res.json(tickets);
    } catch (error) {
        console.error('Error fetching tickets:', error);
        res.status(500).json({ message: "Error fetching tickets", error: error.message });
    }
});

// Get ticket statistics
app.get("/api/tickets/statistics", isAuthenticated, async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const total = await Ticket.countDocuments({ status: { $ne: 'deleted' } });
    const resolved = await Ticket.countDocuments({ status: 'resolved' });
    const pending = await Ticket.countDocuments({ status: { $in: ['open', 'in-progress'] } });
    const overdue = await Ticket.countDocuments({ 
      status: { $nin: ['resolved', 'Resolved', 'deleted'] },  // Check for both cases and exclude deleted
      dueDate: { $lt: today }  // Due date is in the past
    });

    res.status(200).json({
      total,
      resolved,
      pending,
      overdue
    });
  } catch (error) {
    console.error("❌ Error fetching ticket statistics:", error);
    res.status(500).json({ message: "Error fetching ticket statistics", error: error.message });
  }
});

// Get specific ticket
app.get("/api/tickets/:id", isAuthenticated, async (req, res) => {
    try {
        const ticket = await Ticket.findById(req.params.id)
            .select('projectId customerCode projectName ticketHeading ticketType status createdDate dueDate projectManager assignedTo supportingTeam instructions');
        if (!ticket) {
            return res.status(404).json({ message: "Ticket not found" });
        }
        res.json(ticket);
    } catch (error) {
        console.error('Error fetching ticket:', error);
        res.status(500).json({ message: "Error fetching ticket", error: error.message });
    }
});

// Update ticket
app.put("/api/tickets/:id", isAuthenticated, async (req, res) => {
    try {
        const ticketId = req.params.id;
        
        // Get ticket before update for audit logging
        const ticketBefore = await Ticket.findById(ticketId).lean();
        if (!ticketBefore) {
            return res.status(404).json({ message: "Ticket not found" });
        }
        
        const {
            projectId,
            customerCode,
            projectName,
            ticketHeading,
            ticketType,
            status,
            dueDate,
            projectManager,
            assignedTo,
            instructions
        } = req.body;

        // Validate ticket type
        if (!['First', 'Cust Alt', 'Rework'].includes(ticketType)) {
            return res.status(400).json({ message: "Invalid ticket type" });
        }

        const ticket = await Ticket.findByIdAndUpdate(
            ticketId,
            {
                projectId,
                customerCode,
                projectName,
                ticketHeading,
                ticketType,
                status,
                dueDate,
                projectManager,
                assignedTo,
                instructions
            },
            { new: true }
        );

        if (!ticket) {
            return res.status(404).json({ message: "Ticket not found" });
        }

        // Create audit log for ticket update using ticketNumber as entityId
        const changes = {
            before: ticketBefore,
            after: ticket.toObject()
        };
        
        await createAuditLog(
            req,
            'UPDATE',
            'ticket',
            ticketBefore.ticketNumber, // Use ticketNumber as entityId
            `Ticket ${ticketBefore.ticketNumber} updated`,
            changes
        );

        res.json(ticket);
    } catch (error) {
        console.error('Error updating ticket:', error);
        res.status(500).json({ message: "Error updating ticket", error: error.message });
    }
}); // Update ticket route

// Generate PDF
app.get("/api/tickets/:id/pdf", isAuthenticated, async (req, res) => {
    try {
        const ticket = await Ticket.findById(req.params.id);
        if (!ticket) return res.status(404).json({ message: "Ticket not found" });

        // Create a sanitized filename from the ticket heading
        const sanitizedHeading = ticket.ticketHeading
            ? ticket.ticketHeading.replace(/[^a-z0-9]/gi, '_').substring(0, 50)
            : ticket._id;
        
        const doc = new PDFDocument({
            margins: {
                top: 50,
                bottom: 50,
                left: 50,
                right: 50
            }
        });
        
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=${sanitizedHeading}.pdf`);
        doc.pipe(res);

        // Define page dimensions and positions first
        const pageWidth = doc.page.width;
        const leftStartX = 50;
        const rightStartX = pageWidth - 250;
        let currentY = 120;
        const lineHeight = 20;

        // Add ticket number at top right with smaller font
        doc.font('Helvetica-Bold')
           .fontSize(10)
           .fillColor('#2c3e50')
           .text(`Ticket #: ${ticket.ticketNumber}`, 
                 rightStartX, 
                 50, 
                 { align: 'right', width: 200 });

        // Reset cursor position for centered header
        doc.moveUp();

        // Add centered header with proper width parameter
        doc.font('Helvetica-Bold')
           .fontSize(16)
           .fillColor('#2c3e50')
           .text('Ticket Details', 
                 leftStartX, 
                 doc.y, 
                 { 
                     align: 'center',
                     width: pageWidth - (leftStartX * 2)
                 });

        doc.moveDown(1);

        // Define styles
        const labelStyle = {
            font: 'Helvetica-Bold',
            fontSize: 12,
            color: '#2c3e50'
        };

        const valueStyle = {
            font: 'Helvetica',
            fontSize: 12,
            color: '#000000'
        };

        // Add Ticket Heading (new)
        doc.font(labelStyle.font)
           .fontSize(labelStyle.fontSize)
           .fillColor(labelStyle.color)
           .text('Ticket Heading: ', leftStartX, currentY, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(ticket.ticketHeading || 'N/A');

        // Project ID
        currentY += lineHeight;
        doc.font(labelStyle.font)
           .fontSize(labelStyle.fontSize)
           .fillColor(labelStyle.color)
           .text('Project ID: ', leftStartX, currentY, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(ticket.projectId);

        // Customer Code
        currentY += lineHeight;
        doc.font(labelStyle.font)
           .fillColor(labelStyle.color)
           .text('Customer Code: ', leftStartX, currentY, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(ticket.customerCode);

        // Project Name
        currentY += lineHeight;
        doc.font(labelStyle.font)
           .fillColor(labelStyle.color)
           .text('Project Name: ', leftStartX, currentY, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(ticket.projectName);

        // Project Manager
        currentY += lineHeight;
        doc.font(labelStyle.font)
           .fillColor(labelStyle.color)
           .text('Project Manager: ', leftStartX, currentY, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(ticket.projectManager || 'N/A');

        // Assigned To
        currentY += lineHeight;
        doc.font(labelStyle.font)
           .fillColor(labelStyle.color)
           .text('Assigned To: ', leftStartX, currentY, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(ticket.assignedTo || 'N/A');

        // Right side dates
        let dateBoxY = 120;
        doc.rect(rightStartX, dateBoxY, 200, lineHeight * 3)
           .fillAndStroke('#f5f5f5', '#cccccc');
        
        // Created Date
        doc.font(labelStyle.font)
           .fillColor(labelStyle.color)
           .text('Created Date: ', rightStartX + 10, dateBoxY + 15, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(new Date(ticket.createdDate).toLocaleDateString());

        // Due Date
        doc.font(labelStyle.font)
           .fillColor(labelStyle.color)
           .text('Due Date: ', rightStartX + 10, dateBoxY + lineHeight + 15, { continued: true })
           .font(valueStyle.font)
           .fillColor(valueStyle.color)
           .text(new Date(ticket.dueDate).toLocaleDateString());

        // Instructions Section
        currentY += lineHeight * 2;
        doc.font(labelStyle.font)
           .fontSize(14)
           .fillColor(labelStyle.color)
           .text('Instructions:', leftStartX, currentY);

        currentY += lineHeight;

        // Improved function to handle Quill content with proper bullet points
        function renderQuillContent(doc, html, x, y, options) {
            // Set default font and size for text
            doc.font('Helvetica').fontSize(10).fillColor('#000000');
            
            // First, extract list items
            const listItems = [];
            const listItemRegex = /<li>(.*?)<\/li>/g;
            let listMatch;
            
            while ((listMatch = listItemRegex.exec(html)) !== null) {
                listItems.push(listMatch[1].trim());
            }
            
            // Clean HTML for regular text
            let cleanHtml = html
                .replace(/<p>(.*?)<\/p>/g, '$1\n\n')  // Replace paragraphs with their content and double line break
                .replace(/<br\s*\/?>/g, '\n')         // Replace <br> with line break
                .replace(/<ul>.*?<\/ul>/gs, '')       // Remove entire lists (we'll handle them separately)
                .replace(/<ol>.*?<\/ol>/gs, '')       // Remove ordered lists
                .replace(/<li>.*?<\/li>/g, '')        // Remove any remaining list items
                .replace(/<strong>(.*?)<\/strong>/g, '$1') // Keep text inside strong tags
                .replace(/<em>(.*?)<\/em>/g, '$1')    // Keep text inside em tags
                .replace(/<u>(.*?)<\/u>/g, '$1')      // Keep text inside u tags
                .replace(/<[^>]*>/g, '')              // Remove any remaining HTML tags
                .replace(/&nbsp;/g, ' ')              // Replace &nbsp; with space
                .replace(/&lt;/g, '<')                // Replace &lt; with <
                .replace(/&gt;/g, '>')                // Replace &gt; with >
                .replace(/&amp;/g, '&')               // Replace &amp; with &
                .replace(/\n\n+/g, '\n\n')            // Replace multiple line breaks with double line break
                .trim();                              // Trim whitespace
            
            // Add regular text to document
            if (cleanHtml) {
                doc.text(cleanHtml, x, y, {
                    width: options.width,
                    align: 'left',
                    lineBreak: true
                });
                
                // Update y position
                y = doc.y + 10; // Add some space before the list
            }
            
            // Now add the list items with proper bullets
            if (listItems.length > 0) {
                const bulletRadius = 2;
                const textX = x + 15; // Indent for text after bullet
                const bulletX = x + 5; // Position for the bullet
                
                listItems.forEach((item, index) => {
                    // Clean the list item text
                    const cleanItem = item
                        .replace(/<strong>(.*?)<\/strong>/g, '$1')
                        .replace(/<em>(.*?)<\/em>/g, '$1')
                        .replace(/<u>(.*?)<\/u>/g, '$1')
                        .replace(/<[^>]*>/g, '')
                        .replace(/&nbsp;/g, ' ')
                        .replace(/&lt;/g, '<')
                        .replace(/&gt;/g, '>')
                        .replace(/&amp;/g, '&')
                        .trim();
                    
                    // Draw bullet point
                    doc.circle(bulletX, y + 4, bulletRadius)
                       .fill();
                    
                    // Calculate text height to know where to place the next bullet
                    const textHeight = doc.heightOfString(cleanItem, {
                        width: options.width - 15, // Adjust width to account for bullet indent
                        align: 'left'
                    });
                    
                    // Add text next to bullet
                    doc.text(cleanItem, textX, y, {
                        width: options.width - 15,
                        align: 'left',
                        lineBreak: true
                    });
                    
                    // Move to position for next bullet
                    y = doc.y + 5; // Add some space between list items
                });
            }
            
            // Return the new Y position
            return doc.y;
        }

        // Render the instructions using the improved function
        currentY = renderQuillContent(doc, ticket.instructions || '', 
            leftStartX, 
            currentY, 
            {
                width: doc.page.width - (leftStartX * 2) - 20
            }
        );

        // Only add footer if there's actual content on the page
        if (currentY > 100) {
            // Footer
            doc.fontSize(12)
               .fillColor('#666666')
               .text(
                   `Generated on ${new Date().toLocaleString()}`,
                   50,
                   doc.page.height - 50,
                   { align: 'center' }
               );
        }

        doc.end();
    } catch (error) {
        console.error('PDF Generation Error:', error);
        res.status(500).json({ message: "Error generating PDF", error: error.message });
    }
});

// Soft delete ticket (mark as deleted instead of removing)
app.delete("/api/tickets/:id", isAuthenticated, async (req, res) => {
    try {
        const ticketId = req.params.id;
        
        // Get ticket before soft deletion for audit logging
        const ticket = await Ticket.findById(ticketId);
        if (!ticket) {
            return res.status(404).json({ message: "Ticket not found" });
        }
        
        // Soft delete by updating status to 'deleted'
        const updatedTicket = await Ticket.findByIdAndUpdate(
            ticketId,
            { status: 'deleted' },
            { new: true }
        );
        
        // Create audit log for ticket soft deletion using ticketNumber as entityId
        await createAuditLog(
            req,
            'DELETE',
            'ticket',
            ticket.ticketNumber, // Use ticketNumber as entityId
            `Ticket ${ticket.ticketNumber} soft deleted (status changed to deleted)`,
            {
                before: ticket.toObject(),
                after: updatedTicket.toObject()
            }
        );
        
        res.json({ message: "Ticket deleted successfully" });
    } catch (error) {
        res.status(500).json({ message: "Error deleting ticket", error: error.message });
    }
}); 

const { createAuditLog } = require('./middleware/auditLogger');

// Update ticket status endpoint with audit logging
app.patch('/api/tickets/:ticketId/status', isAuthenticated, async (req, res) => {
    try {
        const { ticketId } = req.params;
        const { status } = req.body;
        
        // Get the ticket before update for audit logging
        const ticketBefore = await Ticket.findById(ticketId);
        if (!ticketBefore) {
            return res.status(404).json({ error: 'Ticket not found' });
        }

        // Validate status
        if (!['open', 'in-progress', 'on-hold', 'completed', 'delivered', 'deleted'].includes(status)) {
            return res.status(400).json({ error: 'Invalid status value' });
        }

        // Update the ticket status
        const updatedTicket = await Ticket.findByIdAndUpdate(
            ticketId,
            { status },
            { new: true }
        );

        // Create audit log using ticketNumber as entityId
        await createAuditLog(
            req,
            'UPDATE',
            'ticket',
            ticketBefore.ticketNumber, // Use ticketNumber as entityId
            `Ticket status changed from ${ticketBefore.status} to ${status}`,
            {
                before: { status: ticketBefore.status },
                after: { status }
            }
        );

        res.json(updatedTicket);
    } catch (error) {
        console.error('Error updating ticket status:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get overdue tickets
app.get("/api/tickets/overdue", isAuthenticated, async (req, res) => {
    try {
        const overdueTickets = await Ticket.find({ 
            status: { $nin: ['resolved', 'Resolved', 'deleted'] },  // Check for both cases and exclude deleted
            dueDate: { $lt: new Date() }  // Due date is in the past
        });

        res.status(200).json(overdueTickets);
    } catch (error) {
        console.error("❌ Error fetching overdue tickets:", error);
        res.status(500).json({ message: "Error fetching overdue tickets", error: error.message });
    }
});

// Update project status endpoint with audit logging
app.post("/api/project-status/update", isAuthenticated, async (req, res) => {
    try {
        const { projectId, level, rowIndex, columnIndex, newStatus } = req.body;
        
        if (!projectId || level === undefined || rowIndex === undefined || columnIndex === undefined || !newStatus) {
            return res.status(400).json({ 
                message: "Missing required fields",
                received: { projectId, level, rowIndex, columnIndex, newStatus }
            });
        }

        console.log("📝 Updating status for project:", {
            projectId,
            level,
            rowIndex,
            columnIndex,
            newStatus
        });

        const projectStatus = await ProjectStatus.findOne({ 
            projectId: `${projectId}-level-${level}`
        });

        if (!projectStatus) {
            console.error("❌ Project status not found:", `${projectId}-level-${level}`);
            return res.status(404).json({ message: "Project status not found" });
        }

        // Get the old status for audit logging
        const oldStatus = projectStatus.statusData.rows[rowIndex][columnIndex];

        // Ensure the indices are valid
        if (!projectStatus.statusData?.rows?.[rowIndex]) {
            return res.status(400).json({ message: "Invalid row index" });
        }

        // Update the specific cell
        projectStatus.statusData.rows[rowIndex][columnIndex] = newStatus;
        await projectStatus.save();

        // Create audit log
        await createAuditLog(
            req,
            'UPDATE',
            'projectStatus',
            `${projectId}-level-${level}`,
            `Project status cell updated at row ${rowIndex}, column ${columnIndex}`,
            {
                before: { status: oldStatus },
                after: { status: newStatus },
                location: { row: rowIndex, column: columnIndex }
            }
        );

        res.status(200).json({
            message: "Status updated successfully",
            updatedStatus: projectStatus.statusData
        });

    } catch (error) {
        console.error("❌ Error updating project status:", error);
        res.status(500).json({ 
            message: "Failed to update status",
            error: error.message 
        });
    }
});

// Get all users (admin only)
app.get("/api/users", isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), async (req, res) => {
  try {
    const users = await User.find({})
      .select('-password')
      .lean();
    
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Error fetching users' });
  }
});

// Add this new endpoint to fetch Creative Service users
app.get("/api/users/creative-service", isAuthenticated, async (req, res) => {
  try {
    const creativeUsers = await User.find(
      { role: "Creative Service" },
      { username: 1, _id: 1 }
    );
    res.json(creativeUsers);
  } catch (error) {
    console.error('Error fetching Creative Service users:', error);
    res.status(500).json({ message: 'Error fetching users' });
  }
});

// Get users by role - Project Managers
app.get('/api/users/project-managers', isAuthenticated, async (req, res) => {
    try {
        const projectManagers = await User.find({ role: 'Project Manager' })
            .select('username email role _id');
        res.json(projectManagers);
    } catch (error) {
        console.error('Error fetching project managers:', error);
        res.status(500).json({ message: 'Error fetching project managers' });
    }
});

// Add this endpoint to fetch all users for supporting team
app.get("/api/users/all", isAuthenticated, async (req, res) => {
  try {
    const users = await User.find({}, { username: 1, _id: 1 });
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Error fetching users' });
  }
});

// Get user by ID
app.get("/api/users/:userId", isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.params.userId).select('username');
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ message: "Error fetching user", error: error.message });
  }
});

// Delete user (admin only)
app.delete("/api/users/:userId", isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
    await User.findByIdAndDelete(req.params.userId);
    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Error deleting user' });
  }
});

// Update user (admin only)
app.patch("/api/users/:userId", isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
    const userId = req.params.userId;
    const updates = {};
    
    // Get user before update for audit logging
    const userBefore = await User.findById(userId).lean();
    if (!userBefore) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (req.body.password) {
      updates.password = await bcrypt.hash(req.body.password, 10);
    }
    if (req.body.role) {
      updates.role = req.body.role;
    }
    if (req.body.email) {
      updates.email = req.body.email;
    }
    if (req.body.dob) {
      updates.dob = new Date(req.body.dob);
    }
    if (req.body.empId) {
      updates.empId = req.body.empId;
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Create audit log for user update
    const changesObj = {
      before: {},
      after: {}
    };
    
    // Only include changed fields in the audit log
    Object.keys(updates).forEach(key => {
      // Don't include password in audit log for security
      if (key !== 'password') {
        changesObj.before[key] = userBefore[key];
        changesObj.after[key] = updatedUser[key];
      } else {
        changesObj.before[key] = '********';
        changesObj.after[key] = '********';
      }
    });

    await createAuditLog(
      req,
      'UPDATE',
      'user',
      userId,
      `User ${userBefore.username} updated`,
      changesObj
    );

    // Remove password from response
    const userResponse = updatedUser.toObject();
    delete userResponse.password;

    res.json(userResponse);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Error updating user', error: error.message });
  }
});

// Add these new endpoints after your existing user-related endpoints

// First, ensure these directories exist



// Add graceful shutdown at the end of your file
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    console.log('MongoDB connection closed through app termination');
    process.exit(0);
  } catch (err) {
    console.error('Error during shutdown:', err);
    process.exit(1);
  }
});

// Create uploads directory if it doesn't exist
const uploadDir = path.join(__dirname, 'uploads');
const avatarDir = path.join(uploadDir, 'avatars');

if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir);
}
if (!fs.existsSync(avatarDir)) {
    fs.mkdirSync(avatarDir);
}



// Serve uploaded files statically
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Get user profile
// Add this middleware to handle errors

app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ 
    message: 'Internal server error', 
    error: process.env.NODE_ENV === 'production' ? 'An unexpected error occurred' : err.message 
  });
});
app.get("/api/user/profile", isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .select('-password')
      .lean();

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
// Ensure avatar URL is always provided
    user.avatarUrl = user.avatarUrl || defaultAvatarUrl;
    
    res.json(user);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Error fetching user profile' });
  }
});

// Update user profile
app.put("/api/user/profile", isAuthenticated, async (req, res) => {
  try {
    const { phone, location, department } = req.body;
    
    const updates = {};
    if (phone) updates.phone = phone;
    if (location) updates.location = location;
    if (department) updates.department = department;
    updates.lastActive = new Date();

    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    res.json(user);
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ message: 'Error updating user profile' });
  }
});

// Upload avatar
app.post("/api/user/avatar", isAuthenticated, (req, res) => {
  // Use a try-catch block around the multer middleware
  try {
    avatarUpload.single('avatar')(req, res, async (err) => {
      if (err) {
        console.error("❌ Multer error:", err);
        return res.status(400).json({ 
          message: "Error uploading file: " + err.message,
          error: err.message,
          avatarUrl: 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'
        });
      }
      
      try {
        if (!req.file) {
          return res.status(400).json({ 
            message: "No file uploaded",
            avatarUrl: 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp' 
          });
        }

        // Cloudinary returns the URL in req.file.path
        const avatarUrl = req.file.path;
        console.log("✅ File uploaded to Cloudinary:", avatarUrl);
        
        const user = await User.findByIdAndUpdate(
          req.user.id,
          { 
            $set: { 
              avatarUrl,
              lastActive: new Date()
            } 
          },
          { new: true }
        ).select('-password');

        if (!user) {
          return res.status(404).json({ 
            message: "User not found",
            avatarUrl: 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'
          });
        }

        res.json({ 
          message: "Avatar uploaded successfully",
          avatarUrl: user.avatarUrl || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'
        });
      } catch (error) {
        console.error("❌ Error updating user with avatar:", error);
        res.status(500).json({ 
          message: "Error updating user with avatar",
          error: error.message,
          avatarUrl: 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'
        });
      }
    });
  } catch (error) {
    console.error("❌ Error in avatar upload route:", error);
    res.status(500).json({ 
      message: "Server error during upload",
      error: error.message,
      avatarUrl: 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'
    });
  }
});

// Add a specific endpoint for success.png
app.get('/api/success-icon-url', isAuthenticated, (req, res) => {
  try {
    // Check if Cloudinary is configured
    const config = cloudinary.config();
    if (!config.cloud_name || !config.api_key || !config.api_secret) {
      return res.json({ url: 'success.png' });
    }
    
    // Generate URL using the specific public ID
    const url = cloudinary.url('success_tayqq4', {
      secure: true
    });
    
    console.log(`Success icon URL: ${url}`);
    res.json({ url });
  } catch (error) {
    console.error('Error generating success icon URL:', error);
    res.json({ url: 'success.png' });
  }
});

app.get("/api/user/activity", isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .select('lastActive username role')
      .lean();

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Return an array of activities to match the frontend expectations
    const activities = [
      {
        type: 'login',
        description: `${user.username} logged in`,
        timestamp: user.lastActive || new Date()
      },
      // You can add more activity types here as needed
    ];

    res.json(activities);
  } catch (error) {
    console.error('Error fetching user activity:', error);
    res.status(500).json({ message: 'Error fetching user activity' });
  }
});

// Add this new endpoint to fetch project details
app.get("/api/projects/:projectId", isAuthenticated, async (req, res) => {
  try {
    const project = await Project.findById(req.params.projectId);
    if (!project) {
      return res.status(404).json({ message: "Project not found" });
    }
    res.json({
      clientName: project.clientName,
      title: project.title,
      projectManager: project.projectManager,
      supportingTeam: project.supportingTeam || [] // Add this line to include supporting team
    });
  } catch (error) {
    console.error("Error fetching project details:", error);
    res.status(500).json({ message: "Error fetching project details" });
  }
});

// Update supporting team endpoint
app.patch("/api/tickets/:id", isAuthenticated, async (req, res) => {
  try {
      const { id } = req.params;
      const { supportingTeam } = req.body;

      // Get ticket before update for audit logging
      const ticketBefore = await Ticket.findById(id);
      if (!ticketBefore) {
          return res.status(404).json({ message: "Ticket not found" });
      }

      // Validate input
      if (!Array.isArray(supportingTeam)) {
          return res.status(400).json({ 
              message: "Supporting team must be an array of usernames" 
          });
      }

      const updatedTicket = await Ticket.findByIdAndUpdate(
          id,
          { $set: { supportingTeam } },
          { new: true }
      );

      if (!updatedTicket) {
          return res.status(404).json({ message: "Ticket not found" });
      }

      // Create audit log using ticketNumber as entityId
      await createAuditLog(
          req,
          'UPDATE',
          'ticket',
          ticketBefore.ticketNumber, // Use ticketNumber as entityId
          `Supporting team updated for ticket ${ticketBefore.ticketNumber}`,
          {
              before: { supportingTeam: ticketBefore.supportingTeam },
              after: { supportingTeam }
          }
      );

      res.json(updatedTicket);
  } catch (error) {
      console.error('Error updating supporting team:', error);
      res.status(500).json({ 
          message: "Failed to update supporting team",
          error: error.message 
      });
  }
});

// Add this new endpoint to calculate cumulative project WIP
app.get('/api/project-cumulative-wip/:projectId', isAuthenticated, async (req, res) => {
    const { projectId } = req.params;

    try {
        console.log(`Calculating cumulative WIP for projectId: ${projectId}`);

        // Use regex to match all levels of the given projectId
        const projectWIPData = await mongoose.connection.db.collection("projectwip").find({ 
            projectId: new RegExp(`^${projectId}-level-`, 'i') 
        }).toArray();

        if (projectWIPData.length === 0) {
            console.error(`❌ No WIP data found for projectId: ${projectId}`);
            return res.status(404).send('WIP data not found');
        }

        // Initialize counters for the cumulative values
        let cumulativeYetToStart = 0;
        let cumulativeInProgress = 0;
        let cumulativeCompleted = 0;
        let totalLevels = projectWIPData.length;

        // Process each level's data
        projectWIPData.forEach(levelData => {
            const columnCounts = levelData.columnCounts;
            
            // Sum up the weighted ratios across all columns for this level
            let levelYetToStart = 0;
            let levelInProgress = 0;
            let levelCompleted = 0;
            
            Object.values(columnCounts).forEach(column => {
                levelYetToStart += column["Weighted Ratio (Yet to Start %)"] || 0;
                levelInProgress += column["Weighted Ratio (In Progress %)"] || 0;
                levelCompleted += column["Weighted Ratio (Completed %)"] || 0;
            });
            
            // Add to the cumulative totals
            cumulativeYetToStart += levelYetToStart;
            cumulativeInProgress += levelInProgress;
            cumulativeCompleted += levelCompleted;
        });

        // Calculate the averages
        const averageYetToStart = cumulativeYetToStart / totalLevels;
        const averageInProgress = cumulativeInProgress / totalLevels;
        const averageCompleted = cumulativeCompleted / totalLevels;

        // Return the cumulative WIP data
        res.status(200).json({
            projectId,
            totalLevels,
            cumulativeWIP: {
                yetToStart: averageYetToStart,
                inProgress: averageInProgress,
                completed: averageCompleted
            }
        });
    } catch (error) {
        console.error('❌ Error calculating cumulative WIP:', error);
        res.status(500).send('Error calculating cumulative WIP');
    }
});

// Add this endpoint to get cumulative WIP for all projects
app.get('/api/project-cumulative-wip', isAuthenticated, async (req, res) => {
    try {
        // Run the aggregation to ensure data is up-to-date
        await runCumulativeWIPAggregation();
        
        // Fetch the results using the Mongoose model
        const cumulativeWIPData = await ProjectCumulativeWIP.find({})
            .lean()
            .exec();
            
        if (cumulativeWIPData.length === 0) {
            console.log("⚠️ No documents found in projectCumulativeWIP collection");
            return res.status(404).json({ message: "No cumulative WIP data found" });
        }
        
        console.log(`✅ Found ${cumulativeWIPData.length} documents in projectCumulativeWIP collection`);
        res.status(200).json(cumulativeWIPData);
    } catch (error) {
        console.error("❌ Error fetching cumulative WIP data:", error);
        res.status(500).json({ message: "Error fetching cumulative WIP data" });
    }
});

// Get all projects for a parent project ID
app.get("/api/projects/parent/:parentId", isAuthenticated, async (req, res) => {
  try {
    const { parentId } = req.params;
    
    // Find all projects with the given parentProjectId
    const projects = await InProgressProject.find({ parentProjectId: parentId });
    
    res.status(200).json(projects);
  } catch (error) {
    console.error("❌ Error fetching projects by parent ID:", error);
    res.status(500).json({ message: "Error fetching projects" });
  }
});

// Add this endpoint to get all projects from all collections
app.get("/api/all-projects", isAuthenticated, async (req, res) => {
  try {
    // Fetch projects from all three collections
    const yetToStartProjects = await YetToStartProject.find({}).lean();
    const inProgressProjects = await InProgressProject.find({}).lean();
    const completedProjects = await CompletedProject.find({}).lean();
    
    // Combine all projects
    const allProjects = [
      ...yetToStartProjects,
      ...inProgressProjects,
      ...completedProjects
    ];
    
    res.status(200).json(allProjects);
  } catch (error) {
    console.error("❌ Error fetching all projects:", error);
    res.status(500).json({ message: "Error fetching projects" });
  }
});

// Get audit logs - Admin only
app.get("/api/audit-logs", isAuthenticated, async (req, res) => {
  try {
      // Check if user is admin
      if (req.user.role !== 'Admin') {
          return res.status(403).json({ message: "Access denied" });
      }

      const { 
          entityType, 
          entityId, 
          action, 
          userId,
          startDate,
          endDate,
          limit = 100,
          page = 1
      } = req.query;

      // Build query
      const query = {};
      if (entityType) query.entityType = entityType;
      if (entityId) query.entityId = entityId;
      if (action) query.action = action;
      if (userId) query['performedBy.userId'] = userId;
      
      // Date range
      if (startDate || endDate) {
          query.timestamp = {};
          if (startDate) query.timestamp.$gte = new Date(startDate);
          if (endDate) query.timestamp.$lte = new Date(endDate);
      }

      // Calculate pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      // Get total count for pagination
      const total = await AuditLog.countDocuments(query);
      
      // Get logs with pagination and sorting
      const logs = await AuditLog.find(query)
          .sort({ timestamp: -1 })
          .skip(skip)
          .limit(parseInt(limit));
      
      res.json({
          logs,
          pagination: {
              total,
              page: parseInt(page),
              limit: parseInt(limit),
              pages: Math.ceil(total / parseInt(limit))
          }
      });
  } catch (error) {
      console.error("Error fetching audit logs:", error);
      res.status(500).json({ message: "Error fetching audit logs" });
  }
});

// Add Cloudinary helper functions
function getPublicIdFromUrl(url) {
  // Extract public_id from Cloudinary URL
  const regex = /\/v\d+\/([^/]+)\.\w+$/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

// Add a function to delete files from Cloudinary
async function deleteFileFromCloudinary(fileUrl) {
  try {
    const publicId = getPublicIdFromUrl(fileUrl);
    if (publicId) {
      const result = await cloudinary.uploader.destroy(publicId);
      console.log(`File deleted from Cloudinary: ${publicId}`, result);
      return result;
    }
    return null;
  } catch (error) {
    console.error('Error deleting file from Cloudinary:', error);
    throw error;
  }
}

// Update file deletion endpoint
app.delete("/api/files/:fileId", isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), async (req, res) => {
  try {
    const fileId = req.params.fileId;
    
    // Find the file to get its Cloudinary URL
    const file = await File.findById(fileId);
    if (!file) {
      return res.status(404).json({ message: "File not found" });
    }
    
    // Delete from Cloudinary first
    if (file.fileUrl) {
      await deleteFileFromCloudinary(file.fileUrl);
    }
    
    // Then delete from database
    await File.findByIdAndDelete(fileId);
    
    // Create audit log for file deletion
    await createAuditLog(
      req,
      'DELETE',
      'file',
      `${file.projectId}-${file.stageIndex}-${fileId}`,
      `File "${file.originalName}" deleted from approval stage ${file.stageIndex}`,
      {
        before: file.toObject(),
        after: null
      }
    );
    
    res.json({ message: "File deleted successfully" });
  } catch (error) {
    console.error("Error deleting file:", error);
    res.status(500).json({ message: "Error deleting file", error: error.message });
  }
});

async function getDefaultAvatarUrl() {
  try {
    // Upload the default avatar to Cloudinary if it doesn't exist
    const defaultAvatarPath = path.join(__dirname, 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp');
    
    // Check if the file exists
    if (fs.existsSync(defaultAvatarPath)) {
      // Upload to Cloudinary
      const result = await cloudinary.uploader.upload(defaultAvatarPath, {
        folder: 'avatars',
        public_id: 'default-avatar',
        overwrite: true
      });
      
      return result.secure_url;
    }
    
    // If file doesn't exist, return a placeholder URL
    return 'https://res.cloudinary.com/demo/image/upload/v1/sample';
  } catch (error) {
    console.error('Error getting default avatar URL:', error);
    return 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
  }
}

// Initialize default avatar URL on server start
let defaultAvatarUrl = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
(async () => {
  try {
    defaultAvatarUrl = await getDefaultAvatarUrl();
    console.log('Default avatar URL:', defaultAvatarUrl);
  } catch (error) {
    console.error('Error initializing default avatar:', error);
  }
})();


// Add this with your other user-related endpoints, before the app.listen call
// Bulk user import endpoint - Fix the authorization check
app.post("/api/users/import", isAuthenticated, csvUpload.single('userFile'), async (req, res) => {
  try {
    // Fix the authorization check - remove the extra negation
    if (req.user.role !== 'Admin') {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    if (!req.file) {
      return res.status(400).json({ message: "No file uploaded" });
    }

    const filePath = req.file.path;
    const results = [];
    const errors = [];
    
    // Process CSV file
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        results.push(row);
      })
      .on('end', async () => {
        // Process each row
        for (const row of results) {
          try {
            // Validate required fields
            if (!row.empId || !row.username || !row.email || !row.password) {
              errors.push({
                row: row,
                error: 'Missing required fields'
              });
              continue;
            }
            
            // Check if user already exists
            const existingUser = await User.findOne({ 
              $or: [
                { empId: row.empId },
                { username: row.username },
                { email: row.email }
              ]
            });
            
            if (existingUser) {
              errors.push({
                row: row,
                error: 'User with this empId, username, or email already exists'
              });
              continue;
            }
            
            // Hash password
            const hashedPassword = await bcrypt.hash(row.password, 10);
            
            // Create user
            const newUser = new User({
              empId: row.empId,
              username: row.username,
              email: row.email,
              password: hashedPassword,
              dob: row.dob ? new Date(row.dob) : new Date('1990-01-01'),
              role: row.role || 'Basic',
              phone: row.phone || '',
              location: row.location || '',
              department: row.department || '',
              position: row.position || '',
              joinDate: row.joinDate ? new Date(row.joinDate) : new Date(),
              avatarUrl: row.avatarUrl || ''
            });
            
            await newUser.save();
          } catch (error) {
            errors.push({
              row: row,
              error: error.message
            });
          }
        }
        
        // Clean up the temporary file
        fs.unlinkSync(filePath);
        
        // Send response
        res.status(200).json({
          message: `Import completed: ${results.length - errors.length} users created, ${errors.length} failed`,
          results: results.filter(row => !errors.find(e => e.row === row)).map(row => ({
            empId: row.empId,
            username: row.username
          })),
          errors
        });
      });
  } catch (error) {
    console.error("❌ Error importing users:", error);
    // Clean up file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({ message: "Error importing users", error: error.message });
  }
});

// Get a single yet to start project by ID
app.get("/api/yettostartprojects/:id", isAuthenticated, async (req, res) => {
  try {
    const projectId = req.params.id;
    const project = await YetToStartProject.findById(projectId);
    
    if (!project) {
      return res.status(404).json({ message: "Project not found" });
    }
    
    res.status(200).json(project);
  } catch (error) {
    console.error("❌ Error fetching project:", error);
    res.status(500).json({ message: "Error fetching project", error: error.message });
  }
});

// Get all yet to start projects
app.get("/api/yettostartprojects", isAuthenticated, async (req, res) => {
  try {
    const projects = await YetToStartProject.find({});
    res.status(200).json(projects);
  } catch (error) {
    console.error("❌ Error fetching projects:", error);
    res.status(500).json({ message: "Error fetching projects", error: error.message });
  }
});

async function loadOverallProjectPieChart() {
  const projectId = getProjectIdFromURL();
  if (!projectId) {
    console.error("Error: No project selected!");
    return;
  }

  try {
    // Fetch the cumulative WIP data for this project
    const response = await fetchWithAuth(`/api/project-cumulative-wip/${projectId}`);
    if (!response.ok) {
      throw new Error("Failed to fetch cumulative WIP data");
    }

    const cumulativeData = await response.json();
    console.log("Cumulative WIP Data:", cumulativeData);

    // Extract the data
    const yetToStart = cumulativeData.yetToStart || 0;
    const inProgress = cumulativeData.inProgress || 0;
    const completed = cumulativeData.completed || 0;

    // Render the pie chart
    renderOverallPieChart(yetToStart, inProgress, completed);
  } catch (error) {
    console.error("Error fetching cumulative WIP data:", error);
    // Render an empty pie chart or error message
    renderOverallPieChart(0, 0, 0);
  }
}

// Add this new endpoint to update level heading and projectId
app.post("/api/project-status/update-level-heading", isAuthenticated, async (req, res) => {
  try {
    const { projectId, levelNumber, newHeading } = req.body;
    
    console.log("📝 Received request to update level heading:", { 
      projectId, 
      levelNumber, 
      newHeading 
    });
    
    if (!projectId || levelNumber === undefined || !newHeading) {
      console.error("❌ Missing required fields:", { projectId, levelNumber, newHeading });
      return res.status(400).json({ 
        message: "Missing required fields",
        received: { projectId, levelNumber, newHeading }
      });
    }

    // Extract the base project ID (without the level part)
    const baseProjectId = projectId.split('-level-')[0];
    
    // Current level identifier (could be a number or a custom string)
    const currentLevelId = `${baseProjectId}-level-${levelNumber}`;
    
    // Extract the custom level name from the heading (e.g., "Level S" -> "S")
    const levelIdentifier = newHeading.replace(/^Level\s+/i, '').trim();
    
    // New level identifier with the custom name
    const newLevelId = `${baseProjectId}-level-${levelIdentifier}`;

    console.log(`🔄 Updating level heading: ${currentLevelId} -> ${newLevelId}`);

    // Find the existing document
    const projectStatus = await ProjectStatus.findOne({ projectId: currentLevelId });
    
    if (!projectStatus) {
      console.error("❌ Project status not found:", currentLevelId);
      return res.status(404).json({ message: "Project status not found" });
    }

    console.log("✅ Found existing project status:", projectStatus.projectId);

    try {
      // Create a new document with the updated projectId
      const updatedStatus = new ProjectStatus({
        projectId: newLevelId,
        statusData: projectStatus.statusData
      });

      // Save the new document
      await updatedStatus.save();
      console.log("✅ Saved new project status with updated ID:", newLevelId);
      
      // Delete the old document
      const deleteResult = await ProjectStatus.deleteOne({ projectId: currentLevelId });
      console.log("✅ Deleted old project status:", deleteResult);

      // Also update any related WIP weightages
      const updateResult = await ProjectWIPweightage.updateOne(
        { projectId: currentLevelId },
        { $set: { projectId: newLevelId } }
      );
      console.log("✅ Updated WIP weightages:", updateResult);

      // Create audit log for level heading update
      await createAuditLog(
        req,
        'UPDATE',
        'projectStatus',
        currentLevelId,
        `Level heading updated from "${levelNumber}" to "${levelIdentifier}"`,
        {
          before: { projectId: currentLevelId },
          after: { projectId: newLevelId }
        }
      );
      console.log("✅ Created audit log for level heading update");

      res.status(200).json({
        message: "Level heading updated successfully",
        oldId: currentLevelId,
        newId: newLevelId
      });
    } catch (innerError) {
      console.error("❌ Error during update operations:", innerError);
      throw innerError;
    }
  } catch (error) {
    console.error("❌ Error updating level heading:", error);
    res.status(500).json({ 
      message: "Failed to update level heading",
      error: error.message 
    });
  }
});

// Update ticket comments
app.put("/api/tickets/:id/comments", isAuthenticated, async (req, res) => {
  try {
    const ticketId = req.params.id;
    const { comments } = req.body;

    // Validate input
    if (comments === undefined) {
      return res.status(400).json({ error: "Comments field is required" });
    }

    // Update the ticket
    const updatedTicket = await models.Ticket.findByIdAndUpdate(
      ticketId,
      { comments },
      { new: true }
    );

    if (!updatedTicket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    // Log the update in audit log
    await AuditLog.create({
      action: 'UPDATE', // or 'UPDATE_TICKET_COMMENTS' if you prefer
      entityType: 'ticket',
      entityId: ticketId,
      description: `Updated ticket comments from "${updatedTicket.comments}" to "${comments}"`,
      changes: {
        before: { comments: updatedTicket.comments },
        after: { comments: comments }
      },
      performedBy: {
        userId: req.user._id?.toString() || req.user.id?.toString(), // ensure it's a string
        username: req.user.username
      },
      additionalInfo: updatedTicket.ticketNumber // or any other info you want to add
      // timestamp will be set automatically
    });

    res.json(updatedTicket);
  } catch (error) {
    console.error('Error updating ticket comments:', error);
    res.status(500).json({ error: "Failed to update ticket comments" });
  }
});


// Role Users API Endpoints
app.get('/api/role-users', isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), async (req, res) => {
  try {
      const roleUsers = await RoleUser.find().populate('userId', 'username');
      
      // Organize users by role
      const organizedUsers = {
          projectManager: roleUsers.filter(ru => ru.role === 'Project Manager').map(ru => ru.userId),
          creativeService: roleUsers.filter(ru => ru.role === 'Creative Service').map(ru => ru.userId),
          quality: roleUsers.filter(ru => ru.role === 'Quality').map(ru => ru.userId),
          editorial: roleUsers.filter(ru => ru.role === 'Editorial').map(ru => ru.userId),
          creativeAcquisition: roleUsers.filter(ru => ru.role === 'Creative Acquisition').map(ru => ru.userId),
          digital: roleUsers.filter(ru => ru.role === 'Digital').map(ru => ru.userId)
      };
      
      res.json(organizedUsers);
  } catch (error) {
      console.error('Error fetching role users:', error);
      res.status(500).json({ error: 'Failed to fetch role users' });
  }
});

app.post('/api/role-users', isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
      const { role, userId } = req.body;
      
      // Check if user already has this role
      const existingRole = await RoleUser.findOne({ role, userId });
      if (existingRole) {
          return res.status(400).json({ error: 'User already has this role' });
      }
      
      const user = await User.findById(userId);
      if (!user) {
          return res.status(404).json({ error: 'User not found' });
      }
      const roleUser = new RoleUser({ role, userId, username: user.username });
      await roleUser.save();
      
      res.status(201).json(roleUser);
  } catch (error) {
      console.error('Error adding role user:', error);
      res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/api/role-users', isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
      const { role, userId } = req.body;
      
      const result = await RoleUser.deleteOne({ role, userId });
      
      if (result.deletedCount === 0) {
          return res.status(404).json({ error: 'Role user not found' });
      }
      
      res.status(200).json({ message: 'Role user removed successfully' });
  } catch (error) {
      console.error('Error removing role user:', error);
      res.status(500).json({ error: 'Internal server error' });
  }
});

// --- Client Management API Endpoints ---

// Get all clients
app.get('/api/clients', isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), async (req, res) => {
  try {
    const clients = await models.Client.find().sort({ clientName: 1 });
    res.json(clients);
  } catch (error) {
    console.error('Error fetching clients:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add a new client
app.post('/api/clients', isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
    const { clientName, clientCode } = req.body;
    if (!clientName || !clientCode) {
      return res.status(400).json({ error: 'Client name and code are required' });
    }
    const existing = await models.Client.findOne({ $or: [ { clientName }, { clientCode } ] });
    if (existing) {
      return res.status(409).json({ error: 'Client name or code already exists' });
    }
    const client = new models.Client({ clientName, clientCode });
    await client.save();
    res.status(201).json(client);
  } catch (error) {
    console.error('Error adding client:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update a client
app.put('/api/clients/:id', isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
    const { clientName, clientCode } = req.body;
    const { id } = req.params;
    if (!clientName || !clientCode) {
      return res.status(400).json({ error: 'Client name and code are required' });
    }
    const existing = await models.Client.findOne({ $or: [ { clientName }, { clientCode } ], _id: { $ne: id } });
    if (existing) {
      return res.status(409).json({ error: 'Client name or code already exists' });
    }
    const client = await models.Client.findByIdAndUpdate(id, { clientName, clientCode }, { new: true });
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }
    res.json(client);
  } catch (error) {
    console.error('Error updating client:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete a client
app.delete('/api/clients/:id', isAuthenticated, authorizeRoles(["Admin"]), async (req, res) => {
  try {
    const { id } = req.params;
    const client = await models.Client.findByIdAndDelete(id);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }
    res.json({ message: 'Client deleted' });
  } catch (error) {
    console.error('Error deleting client:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Project Links API Endpoints
app.get("/api/project-links/:projectId", isAuthenticated, async (req, res) => {
  try {
    const { projectId } = req.params;
    
    // Check if project exists
    const project = await InProgressProject.findById(projectId);
    if (!project) {
      return res.status(404).json({ message: "Project not found" });
    }

    // Get project links from the project document
    const projectLinks = project.projectLinks || [];
    
    res.status(200).json(projectLinks);
  } catch (error) {
    console.error("Error fetching project links:", error);
    res.status(500).json({ message: "Error fetching project links" });
  }
});

app.post("/api/project-links/:projectId", isAuthenticated, async (req, res) => {
  try {
    const { projectId } = req.params;
    const { links } = req.body;
    
    // Validate input
    if (!Array.isArray(links)) {
      return res.status(400).json({ message: "Links must be an array" });
    }

    // Check if project exists
    const project = await InProgressProject.findById(projectId);
    if (!project) {
      return res.status(404).json({ message: "Project not found" });
    }

    // Update project with new links
    project.projectLinks = links;
    await project.save();

    res.status(200).json({ 
      message: "Project links updated successfully",
      links: project.projectLinks 
    });
  } catch (error) {
    console.error("Error updating project links:", error);
    res.status(500).json({ message: "Error updating project links" });
  }
});

app.get('/api/projects', async (req, res) => {
  try {
    // Only return fields needed for the dropdown
    const projects = await Project.find({}, '_id title clientName projectManager');
    res.json(projects);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch projects' });
  }
});

// Redirect unknown routes to login
app.use((req, res, next) => {
  if (req.path.startsWith("/api/")) {
    return res.status(404).json({ message: "API route not found" });
  }
  res.redirect("/login.html");
});



// Function to run the Aggregation Pipeline for "projectstatuses"
async function runAggregation() {
  try {
    console.log("🔄 Running Aggregation Pipeline...");
    const pipeline = [
      {
        $project: {
          projectId: 1,
          columnNames: {
            $slice: [
              "$statusData.headers",
              1,
              {
                $size: "$statusData.headers",
              },
            ],
          },
          columnValues: {
            $map: {
              input: "$statusData.rows",
              as: "row",
              in: {
                $slice: [
                  "$$row",
                  1,
                  {
                    $size: "$$row",
                  },
                ],
              },
            },
          },
        },
      },
      {
        $project: {
          projectId: 1,
          columnCounts: {
            $map: {
              input: {
                $range: [
                  0,
                  {
                    $size: "$columnNames",
                  },
                ],
              },
              as: "index",
              in: {
                k: {
                  $arrayElemAt: ["$columnNames", "$$index"],
                },
                v: {
                  "Yet to Start": {
                    $size: {
                      $filter: {
                        input: "$columnValues",
                        as: "row",
                        cond: {
                          $eq: [
                            {
                              $arrayElemAt: ["$$row", "$$index"],
                            },
                            "Yet to Start",
                          ],
                        },
                      },
                    },
                  },
                  "In Progress": {
                    $size: {
                      $filter: {
                        input: "$columnValues",
                        as: "row",
                        cond: {
                          $eq: [
                            {
                              $arrayElemAt: ["$$row", "$$index"],
                            },
                            "In Progress",
                          ],
                        },
                      },
                    },
                  },
                  Completed: {
                    $size: {
                      $filter: {
                        input: "$columnValues",
                        as: "row",
                        cond: {
                          $eq: [
                            {
                              $arrayElemAt: ["$$row", "$$index"],
                            },
                            "Completed",
                          ],
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      {
        $project: {
          projectId: 1,
          columnCounts: {
            $map: {
              input: "$columnCounts",
              as: "col",
              in: {
                k: "$$col.k",
                v: {
                  "Yet to Start": "$$col.v.Yet to Start",
                  "In Progress": "$$col.v.In Progress",
                  Completed: "$$col.v.Completed",
                  Total: {
                    $add: [
                      "$$col.v.Yet to Start",
                      "$$col.v.In Progress",
                      "$$col.v.Completed",
                    ],
                  },
                  "Ratio (Yet to Start %)": {
                    $cond: {
                      if: {
                        $gt: [
                          {
                            $add: [
                              "$$col.v.Yet to Start",
                              "$$col.v.In Progress",
                              "$$col.v.Completed",
                            ],
                          },
                          0,
                        ],
                      },
                      then: {
                        $multiply: [
                          {
                            $divide: [
                              "$$col.v.Yet to Start",
                              {
                                $add: [
                                  "$$col.v.Yet to Start",
                                  "$$col.v.In Progress",
                                  "$$col.v.Completed",
                                ],
                              },
                            ],
                          },
                          100,
                        ],
                      },
                      else: 0,
                    },
                  },
                  "Ratio (In Progress %)": {
                    $cond: {
                      if: {
                        $gt: [
                          {
                            $add: [
                              "$$col.v.Yet to Start",
                              "$$col.v.In Progress",
                              "$$col.v.Completed",
                            ],
                          },
                          0,
                        ],
                      },
                      then: {
                        $multiply: [
                          {
                            $divide: [
                              "$$col.v.In Progress",
                              {
                                $add: [
                                  "$$col.v.Yet to Start",
                                  "$$col.v.In Progress",
                                  "$$col.v.Completed",
                                ],
                              },
                            ],
                          },
                          100,
                        ],
                      },
                      else: 0,
                    },
                  },
                  "Ratio (Completed %)": {
                    $cond: {
                      if: {
                        $gt: [
                          {
                            $add: [
                              "$$col.v.Yet to Start",
                              "$$col.v.In Progress",
                              "$$col.v.Completed",
                            ],
                          },
                          0,
                        ],
                      },
                      then: {
                        $multiply: [
                          {
                            $divide: [
                              "$$col.v.Completed",
                              {
                                $add: [
                                  "$$col.v.Yet to Start",
                                  "$$col.v.In Progress",
                                  "$$col.v.Completed",
                                ],
                              },
                            ],
                          },
                          100,
                        ],
                      },
                      else: 0,
                    },
                  },
                },
              },
            },
          },
        },
      },
      {
        $group: {
          _id: "$projectId",
          columns: {
            $first: "$columnCounts",
          },
        },
      },
      {
        $project: {
          projectId: "$_id",
          columnCounts: {
            $arrayToObject: "$columns",
          },
          _id: 0,
        },
      },
      {
        $merge: {
          into: "projectStatusSummary",
          on: "projectId",
          whenMatched: "merge",
          whenNotMatched: "insert",
        },
      },
    ];

    const ProjectStatus = mongoose.connection.db.collection("projectstatuses");
    await ProjectStatus.aggregate(pipeline).toArray();
    console.log(
      "✅ Aggregation completed and stored in 'projectStatusSummary'."
    );

    // Run the second aggregation after the first one completes
    await runWIPAggregation();
  } catch (error) {
    console.error("❌ Error running aggregation:", error);
  }
}

// Function to run the new Aggregation Pipeline for "projectstatussummary"
async function runWIPAggregation() {
  try {
    console.log("🔄 Running WIP Aggregation Pipeline...");
    const pipeline = [
      {
        $lookup: {
          from: "projectwipweightages",
          localField: "projectId",
          foreignField: "projectId",
          as: "weightages",
        },
      },
      {
        $unwind: "$weightages",
      },
      {
        $project: {
          projectId: 1,
          columns: {
            $objectToArray: "$columnCounts",
          },
          weightages: "$weightages.weightages",
        },
      },
      {
        $project: {
          projectId: 1,
          columns: {
            $map: {
              input: "$columns",
              as: "col",
              in: {
                k: "$$col.k",
                v: {
                  "Weighted Ratio (Yet to Start %)": {
                    $divide: [
                      {
                        $multiply: [
                          { $ifNull: ["$$col.v.Ratio (Yet to Start %)", 0] },
                          {
                            $arrayElemAt: [
                              "$weightages",
                              { $indexOfArray: ["$columns.k", "$$col.k"] },
                            ],
                          },
                        ],
                      },
                      100,
                    ],
                  },
                  "Weighted Ratio (In Progress %)": {
                    $divide: [
                      {
                        $multiply: [
                          { $ifNull: ["$$col.v.Ratio (In Progress %)", 0] },
                          {
                            $arrayElemAt: [
                              "$weightages",
                              { $indexOfArray: ["$columns.k", "$$col.k"] },
                            ],
                          },
                        ],
                      },
                      100,
                    ],
                  },
                  "Weighted Ratio (Completed %)": {
                    $divide: [
                      {
                        $multiply: [
                          { $ifNull: ["$$col.v.Ratio (Completed %)", 0] },
                          {
                            $arrayElemAt: [
                              "$weightages",
                              { $indexOfArray: ["$columns.k", "$$col.k"] },
                            ],
                          },
                        ],
                      },
                      100,
                    ],
                  },
                },
              },
            },
          },
        },
      },
      {
        $project: {
          projectId: 1,
          columnCounts: {
            $arrayToObject: "$columns",
          },
          _id: 0,
        },
      },
      {
        $merge: {
          into: "projectwip",
          on: "projectId",
          whenMatched: "merge",
          whenNotMatched: "insert",
        },
      },
    ];

    const ProjectStatusSummary = mongoose.connection.db.collection(
      "projectStatusSummary"
    );
    const result = await ProjectStatusSummary.aggregate(pipeline).toArray();
    console.log("✅ WIP Aggregation Result:", result);
    console.log("✅ WIP Aggregation completed and stored in 'projectwip'.");

    // Call the new aggregation pipeline
    await runWeightedWIPAggregation();
  } catch (error) {
    console.error("❌ Error running WIP Aggregation:", error);
  }
}

async function runWeightedWIPAggregation() {
  try {
    console.log("🔄 Running Weighted WIP Aggregation Pipeline...");

    const pipeline = [
      {
        // Step 1: Convert columnCounts object into an array of key-value pairs
        $project: {
          projectId: 1,
          columns: { $objectToArray: "$columnCounts" },
        },
      },
      {
        // Step 2: Unwind the columns array to process each task separately
        $unwind: "$columns",
      },
      {
        // Step 3: Group by projectId and sum each type of Weighted Ratio
        $group: {
          _id: "$projectId",
          totalYetToStart: {
            $sum: "$columns.v.Weighted Ratio (Yet to Start %)",
          },
          totalInProgress: {
            $sum: "$columns.v.Weighted Ratio (In Progress %)",
          },
          totalCompleted: {
            $sum: "$columns.v.Weighted Ratio (Completed %)",
          },
        },
      },
      {
        // Step 4: Rename _id to projectId
        $project: {
          _id: 0,
          projectId: "$_id",
          totalYetToStart: 1,
          totalInProgress: 1,
          totalCompleted: 1,
        },
      },
      {
        // Step 5: Output results to a new collection
        $merge: {
          into: "weightedProjectwipSummaries", // Target collection name
          on: "projectId", // Field to match on
          whenMatched: "replace", // Update existing or replace
          whenNotMatched: "insert", // Insert new
        },
      },
    ];

    const ProjectWIP = mongoose.connection.db.collection("projectwip");
    const result = await ProjectWIP.aggregate(pipeline).toArray();
    console.log("✅ Weighted WIP Aggregation Result:", result);
    console.log("✅ Weighted WIP Aggregation completed and stored in 'weightedProjectwipSummaries'.");
  } catch (error) {
    console.error("❌ Error running Weighted WIP Aggregation:", error);
  }
}

async function watchProjectStatusChanges() {
  try {
    // Only attempt to watch if the connection is established
    if (mongoose.connection.readyState !== 1) {
      console.log("⚠️ Database connection not ready, will retry watching in 5 seconds");
      setTimeout(watchProjectStatusChanges, 5000);
      return;
    }

    console.log("👀 Setting up change stream for ProjectStatus model");
    
    // Use the imported ProjectStatus model
    ProjectStatus.watch().on('change', async (change) => {
      console.log("🔄 Detected a change in ProjectStatus:", change);
      
      // Run your aggregation functions
      try {
        await runAggregation();
        await runWIPAggregation();
        await runWeightedWIPAggregation();
        await runCumulativeWIPAggregation();
      } catch (error) {
        console.error("❌ Error running aggregations:", error);
      }
    });
    
    console.log("✅ Change stream set up successfully");
  } catch (error) {
    console.error("❌ Error setting up change stream:", error);
    // Try again after a delay
    setTimeout(watchProjectStatusChanges, 5000);
  }
}

// Function to run the cumulative WIP aggregation
async function runCumulativeWIPAggregation() {
    try {
        console.log("🔄 Running Cumulative WIP Aggregation Pipeline...");

        // First, get all unique base project IDs (without the level suffix)
        const projectWIP = mongoose.connection.db.collection("projectwip");
        const allProjectLevels = await projectWIP.distinct("projectId");
        
        // Extract base project IDs (remove the -level-X suffix)
        const baseProjectIds = [...new Set(
            allProjectLevels.map(id => {
                const match = id.match(/^(.*?)-level-\d+$/);
                return match ? match[1] : id;
            })
        )];
        
        console.log(`Found ${baseProjectIds.length} unique base projects`);
        
        // Process each base project ID
        for (const baseProjectId of baseProjectIds) {
            // Find all levels for this project
            const levelData = await projectWIP.find({
                projectId: new RegExp(`^${baseProjectId}-level-`, 'i')
            }).toArray();
            
            if (levelData.length === 0) continue;
            
            // Initialize counters
            let totalYetToStart = 0;
            let totalInProgress = 0;
            let totalCompleted = 0;
            
            // Process each level
            levelData.forEach(level => {
                const columnCounts = level.columnCounts;
                
                // Sum up the weighted ratios across all columns for this level
                Object.values(columnCounts).forEach(column => {
                    totalYetToStart += column["Weighted Ratio (Yet to Start %)"] || 0;
                    totalInProgress += column["Weighted Ratio (In Progress %)"] || 0;
                    totalCompleted += column["Weighted Ratio (Completed %)"] || 0;
                });
            });
            
            // Calculate averages
            const totalLevels = levelData.length;
            const averageYetToStart = totalYetToStart / totalLevels;
            const averageInProgress = totalInProgress / totalLevels;
            const averageCompleted = totalCompleted / totalLevels;
            
            // Store the result using Mongoose model
            try {
                const updateResult = await ProjectCumulativeWIP.findOneAndUpdate(
                    { projectId: baseProjectId },
                    { 
                        projectId: baseProjectId,
                        totalLevels,
                        yetToStart: averageYetToStart,
                        inProgress: averageInProgress,
                        completed: averageCompleted,
                        lastUpdated: new Date()
                    },
                    { upsert: true, new: true }
                );
                console.log(`✅ Updated cumulative WIP for project: ${baseProjectId}`);
            } catch (updateError) {
                console.error(`❌ Error updating cumulative WIP for project ${baseProjectId}:`, updateError);
            }
        }
        
        console.log("✅ Cumulative WIP Aggregation completed successfully");
    } catch (error) {
        console.error("❌ Error running Cumulative WIP Aggregation:", error);
    }
}

// Add an endpoint to get cumulative WIP for all projects
app.get('/api/project-cumulative-wip', isAuthenticated, async (req, res) => {
    try {
        // Run the aggregation to ensure data is up-to-date
        await runCumulativeWIPAggregation();
        
        // Check if collection exists
        const collections = await mongoose.connection.db.listCollections({name: "projectCumulativeWIP"}).toArray();
        if (collections.length === 0) {
            console.log("⚠️ projectCumulativeWIP collection does not exist yet");
            return res.status(404).json({ message: "No cumulative WIP data found" });
        }
        
        // Fetch the results
        const cumulativeWIPData = await mongoose.connection.db
            .collection("projectCumulativeWIP")
            .find({})
            .toArray();
            
        if (cumulativeWIPData.length === 0) {
            console.log("⚠️ No documents found in projectCumulativeWIP collection");
            return res.status(404).json({ message: "No cumulative WIP data found" });
        }
        
        console.log(`✅ Found ${cumulativeWIPData.length} documents in projectCumulativeWIP collection`);
        res.status(200).json(cumulativeWIPData);
    } catch (error) {
        console.error("❌ Error fetching cumulative WIP data:", error);
        res.status(500).json({ message: "Error fetching cumulative WIP data" });
    }
});

// Add this to your existing watchProjectStatusChanges function
async function watchProjectStatusChanges() {
    try {
        const collection = mongoose.connection.db.collection("projectstatuses");
        const changeStream = collection.watch();
        console.log("👀 Watching 'projectstatuses' for changes...");
        changeStream.on("change", async (change) => {
            console.log("🔄 Detected a change in 'projectstatuses':", change);
            await runAggregation();
            await runWIPAggregation();
            await runWeightedWIPAggregation();
            await runCumulativeWIPAggregation(); // Add this line
        });
    } catch (error) {
        console.error("❌ Error watching collection:", error);
    }
}

/*
// Define both collections for moving project
const YetToStartProject = mongoose.model("YetToStartProject", projectSchema, "yettostartprojects");
const InProgressProject = mongoose.model("InProgressProject", projectSchema, "inprogressprojects");
const CompletedProject = mongoose.model("CompletedProject", projectSchema, "completedprojects");

// Schema for projectCumulativeWIP
const projectCumulativeWIPSchema = new mongoose.Schema({
  projectId: { type: String, required: true, unique: true },
  totalLevels: { type: Number, default: 0 },
  yetToStart: { type: Number, default: 0 },
  inProgress: { type: Number, default: 0 },
  completed: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: Date.now }
}, { collection: "projectCumulativeWIP" });

const ProjectCumulativeWIP = mongoose.model(
  "ProjectCumulativeWIP",
  projectCumulativeWIPSchema
);
*/

router.post("/api/register", async (req, res) => {
  const { username, password, role, } = req.body;

  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = new User({ username, password: hashedPassword, role });
    await newUser.save();
    res.status(201).json({ message: "User registered successfully" });
  } catch (error) {
    console.error("Error during registration:", error);
    res.status(500).json({ message: "Registration failed" });
  }
});

router.post("/api/login", async (req, res) => {
  const { username, password } = req.body;

  try {
    const user = await User.findOne({ username });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    // Generate a token (optional)
    const token = jwt.sign(
      { id: user._id, role: user.role, username: user.username }, // add username here
      process.env.JWT_SECRET,
      { expiresIn: "1h" }
    );

    res.status(200).json({ 
      message: "Login successful", 
      role: user.role, 
      token, 
      name: user.username,
      id: user._id,
      avatarUrl: user.avatarUrl || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'
    });
  } catch (error) {
    console.error("Error during login:", error);
    res.status(500).json({ message: "Login failed" });
  }
});

router.get("/api/yettostartprojects", isAuthenticated, async (req, res) => {
  try {
    const projects = await YetToStartProject.find({});
    res.status(200).json(projects);
  } catch (error) {
    console.error("Error fetching YetToStart projects:", error);
    res.status(500).json({ message: "Error fetching projects" });
  }
});



router.post("/api/move-to-production", async (req, res) => {
  console.log("Request received for moving project to production:", req.body);
  const { projectId } = req.body;

  try {
    const project = await YetToStartProject.findById(projectId);
    if (!project) {
      return res.status(404).json({ message: "Project not found" });
    }

    // Create a copy of the project for the in-progress collection
    const inProgressProject = new InProgressProject(project.toObject());
    await inProgressProject.save();
    
    // Delete the project from the yet-to-start collection
    await YetToStartProject.findByIdAndDelete(projectId);

    // Create audit log for moving project to production
    // Since this endpoint might not have authentication middleware,
    // we need to handle the case where req.user might be undefined
    if (req.user) {
      await createAuditLog(
        req,
        'UPDATE',
        'project',
        projectId,
        `Project ${project.title || projectId} moved to production`,
        {
          before: { status: 'Yet to Start', collection: 'yettostartprojects' },
          after: { status: 'In Progress', collection: 'inprogressprojects' }
        }
      );
    } else {
      // Create a minimal req object with system user for audit logging
      const auditReq = {
        user: {
          id: 'system',
          username: 'system'
        }
      };
      
      await createAuditLog(
        auditReq,
        'UPDATE',
        'project',
        projectId,
        `Project ${project.title || projectId} moved to production`,
        {
          before: { status: 'Yet to Start', collection: 'yettostartprojects' },
          after: { status: 'In Progress', collection: 'inprogressprojects' }
        }
      );
    }

    console.log(`✅ Project ${projectId} moved to production successfully`);
    res.status(200).json({ message: "Project moved to production successfully" });
  } catch (error) {
    console.error("Error moving project to production:", error);
    res.status(500).json({ message: "Error moving project to production" });
  }
});

router.post("/api/move-to-completed", isAuthenticated, authorizeRoles(["Admin", "Project Manager"]), async (req, res) => {
  const { projectId } = req.body;

  try {
    // Find the project in the in-progress collection
    const project = await InProgressProject.findById(projectId);
    if (!project) {
      console.error(`❌ Project not found for ID: ${projectId}`);
      return res.status(404).json({ message: "Project not found" });
    }

    // Move the project to the completed collection
    const completedProject = new CompletedProject(project.toObject());
    await completedProject.save();
    console.log(`✅ Project moved to completed collection: ${completedProject._id}`);

    // Remove the project from inprogressprojects
    await InProgressProject.findByIdAndDelete(projectId);
    console.log(`✅ Project removed from in-progress collection: ${projectId}`);

    // Initialize approval status for the project
    const approvalStages = [
      { stage: "Reconcilation completed by PM", completed: false, buttonVisibleTo: project.projectManager },
      { stage: "Reconcilation approved by Peter", completed: false, buttonVisibleTo: "Peter" },
      { stage: "Invoice Sent to Client", completed: false, buttonVisibleTo: "Admin" },
      { stage: "Invoice Paid", completed: false, buttonVisibleTo: "Admin" },
      { stage: "Project Archived", completed: false, buttonVisibleTo: "Admin" }
    ];

    const approvalStatus = new ApprovalStatus({
      projectId: completedProject._id, // Use the string _id directly
      stages: approvalStages,
    });

    await approvalStatus.save();
    console.log(`✅ Approval status initialized for project: ${completedProject._id}`);

    // Create audit log for moving project to completed
    await createAuditLog(
      req,
      'UPDATE',
      'project',
      projectId,
      `Project ${project.title || projectId} marked as completed`,
      {
        before: { 
          status: 'In Progress', 
          collection: 'inprogressprojects',
          projectManager: project.projectManager,
          clientName: project.clientName
        },
        after: { 
          status: 'Completed', 
          collection: 'completedprojects',
          approvalStatus: 'initialized'
        }
      }
    );

    res.status(200).json({ message: "Project marked as completed and approval status initialized" });
  } catch (error) {
    console.error("❌ Error moving project to completed:", error);
    res.status(500).json({ message: "Error moving project to completed" });
  }
});

router.get("/api/inprogress-project-count", isAuthenticated, async (req, res) => {
  try {
    const count = await InProgressProject.countDocuments(); // Count all documents in the collection
    res.status(200).json({ count });
  } catch (error) {
    console.error("Error fetching InProgress project count:", error);
    res.status(500).json({ message: "Error fetching project count" });
  }
});

router.get("/api/completedprojects", isAuthenticated, async (req, res) => {
  try {
    const projects = await CompletedProject.find({}); // Fetch all completed projects
    res.status(200).json(projects);
  } catch (error) {
    console.error("Error fetching completed projects:", error);
    res.status(500).json({ message: "Error fetching completed projects" });
  }
});

router.get("/api/completed-project-count", isAuthenticated, async (req, res) => {
  try {
    const count = await CompletedProject.countDocuments(); // Count all documents in the collection
    res.status(200).json({ count });
  } catch (error) {
    console.error("Error fetching completed project count:", error);
    res.status(500).json({ message: "Error fetching completed project count" });
  }
});

router.get("/api/total-project-count", isAuthenticated, async (req, res) => {
  try {
    const yetToStartCount = await YetToStartProject.countDocuments(); // Count projects in yettostartprojects
    const inProgressCount = await InProgressProject.countDocuments(); // Count projects in inprogressprojects
    const completedCount = await CompletedProject.countDocuments(); // Count projects in completedprojects

    const totalCount = yetToStartCount + inProgressCount + completedCount; // Calculate the total count

    res.status(200).json({ totalCount });
  } catch (error) {
    console.error("Error fetching total project count:", error);
    res.status(500).json({ message: "Error fetching total project count" });
  }
});

router.get("/api/inprogressprojects-by-client", isAuthenticated, async (req, res) => {
  try {
    const data = await InProgressProject.aggregate([
      { $group: { _id: "$clientName", count: { $sum: 1 } } }, // Group by clientName and count
      { $sort: { count: -1 } }, // Sort by count in descending order
    ]);
    res.status(200).json(data);
  } catch (error) {
    console.error("Error fetching in-progress projects by client:", error);
    res.status(500).json({ message: "Error fetching data" });
  }
});

// Update the route that's causing the error
router.get("/api/inprogressprojects-by-projectmanager", isAuthenticated, async (req, res) => {
  try {
    console.log("🔄 Fetching in-progress projects by project manager...");
    
    // Check connection state explicitly
    if (mongoose.connection.readyState !== 1) {
      console.log("⚠️ MongoDB not connected, readyState:", mongoose.connection.readyState);
      return res.status(503).json({ 
        message: "Database connection unavailable, please try again later" 
      });
    }
    
    // Use the model directly instead of aggregation if possible
    const projects = await InProgressProject.find({});
    
    // Perform the aggregation in memory if the dataset is small
    const projectManagerCounts = {};
    
    projects.forEach(project => {
      const manager = project.projectManager || 'Unassigned';
      projectManagerCounts[manager] = (projectManagerCounts[manager] || 0) + 1;
    });
    
    // Convert to the format expected by the client
    const data = Object.entries(projectManagerCounts).map(([manager, count]) => ({
      _id: manager,
      count
    }));
    
    // Sort by count in descending order
    data.sort((a, b) => b.count - a.count);
    
    console.log("✅ Successfully fetched project manager data:", data);
    res.status(200).json(data);
  } catch (error) {
    console.error("❌ Error fetching in-progress projects by project manager:", error);
    res.status(500).json({ 
      message: "Error fetching data",
      error: error.message,
      stack: error.stack
    });
  }
});

function authorizeRoles(allowedRoles) {
  return (req, res, next) => {
    const userRole = req.user.role; // Assume `req.user` contains the authenticated user info

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    next();
  };
}

app.use(router);

// Add these new endpoints for project status management

// Update individual status
app.post("/api/project-status/update-status", isAuthenticated, async (req, res) => {
  const { projectId, level, rowIndex, columnIndex, newStatus } = req.body;

  try {
    const levelId = `${projectId}-level-${level}`;
    console.log("🔄 Updating Status:", { levelId, rowIndex, columnIndex, newStatus });

    // Find the existing status document
    const projectStatus = await ProjectStatus.findOne({ projectId: levelId });
    
    if (!projectStatus) {
      return res.status(404).json({ message: "Project status not found" });
    }

    // Update the specific cell in the rows array
    if (projectStatus.statusData.rows[rowIndex]) {
      projectStatus.statusData.rows[rowIndex][columnIndex] = newStatus;
      await projectStatus.save();
    }

    res.status(200).json({ 
      message: "Status updated successfully",
      updatedStatus: projectStatus.statusData 
    });
  } catch (error) {
    console.error("❌ Error updating status:", error);
    res.status(500).json({ message: "Error updating status" });
  }
});

// Get project status
app.get("/api/project-status/:projectId", isAuthenticated, async (req, res) => {
  const { projectId } = req.params;

  try {
    const statuses = await ProjectStatus.find({
      projectId: new RegExp(`^${projectId}-level-`)
    }).sort({ projectId: 1 });

    const formattedStatuses = statuses.map(status => status.statusData);
    res.status(200).json(formattedStatuses);
  } catch (error) {
    console.error("Error fetching project status:", error);
    res.status(500).json({ message: "Error fetching project status" });
  }
});

// Update project status endpoint
app.post("/api/project-status/update", isAuthenticated, async (req, res) => {
    try {
        const { projectId, level, rowIndex, columnIndex, newStatus } = req.body;
        
        if (!projectId || level === undefined || rowIndex === undefined || columnIndex === undefined || !newStatus) {
            return res.status(400).json({ 
                message: "Missing required fields",
                received: { projectId, level, rowIndex, columnIndex, newStatus }
            });
        }

        console.log("📝 Updating status for project:", {
            projectId,
            level,
            rowIndex,
            columnIndex,
            newStatus
        });

        const projectStatus = await ProjectStatus.findOne({ 
            projectId: `${projectId}-level-${level}`
        });

        if (!projectStatus) {
            console.error("❌ Project status not found:", `${projectId}-level-${level}`);
            return res.status(404).json({ message: "Project status not found" });
        }

        // Ensure the indices are valid
        if (!projectStatus.statusData?.rows?.[rowIndex]) {
            return res.status(400).json({ message: "Invalid row index" });
        }

        // Update the specific cell
        projectStatus.statusData.rows[rowIndex][columnIndex] = newStatus;
        await projectStatus.save();

        res.status(200).json({
            message: "Status updated successfully",
            updatedStatus: projectStatus.statusData
        });

    } catch (error) {
        console.error("❌ Error updating project status:", error);
        res.status(500).json({ 
            message: "Failed to update status",
            error: error.message 
        });
    }
}); // Update ticket route



// Update the server startup code at the end of your file
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
  // Connect to MongoDB after server starts
  connectToMongoDB();
});





